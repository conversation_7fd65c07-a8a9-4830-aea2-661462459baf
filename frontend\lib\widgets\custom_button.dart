import 'package:flutter/material.dart';
import '../utils/app_colors.dart';

enum ButtonVariant { primary, secondary, accent, outline, ghost, destructive }

enum ButtonSize { small, medium, large }

class CustomButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final ButtonVariant variant;
  final ButtonSize size;
  final double? width;
  final IconData? icon;
  final IconData? suffixIcon;
  final bool fullWidth;
  final bool disabled;

  const CustomButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.width,
    this.icon,
    this.suffixIcon,
    this.fullWidth = false,
    this.disabled = false,
  });

  // Legacy constructor for backward compatibility
  const CustomButton.legacy({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    bool isOutlined = false,
    this.size = ButtonSize.medium,
    this.width,
    this.icon,
    this.suffixIcon,
    this.fullWidth = false,
    this.disabled = false,
  }) : variant = isOutlined ? ButtonVariant.outline : ButtonVariant.primary;

  @override
  State<CustomButton> createState() => _CustomButtonState();
}

class _CustomButtonState extends State<CustomButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.96,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  ButtonStyle _getButtonStyle() {
    final isEnabled =
        !widget.disabled && !widget.isLoading && widget.onPressed != null;

    // Size configurations
    EdgeInsets padding;
    double height;
    double fontSize;

    switch (widget.size) {
      case ButtonSize.small:
        padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
        height = 36;
        fontSize = 14;
        break;
      case ButtonSize.medium:
        padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
        height = 48;
        fontSize = 16;
        break;
      case ButtonSize.large:
        padding = const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
        height = 56;
        fontSize = 18;
        break;
    }

    // Variant configurations
    Color backgroundColor;
    Color foregroundColor;
    Color? borderColor;

    switch (widget.variant) {
      case ButtonVariant.primary:
        backgroundColor =
            isEnabled ? AppColors.primary : AppColors.surfaceContainer;
        foregroundColor =
            isEnabled ? AppColors.textOnPrimary : AppColors.textDisabled;
        borderColor = null;
        break;
      case ButtonVariant.secondary:
        backgroundColor =
            isEnabled ? AppColors.secondary : AppColors.surfaceContainer;
        foregroundColor =
            isEnabled ? AppColors.textOnPrimary : AppColors.textDisabled;
        borderColor = null;
        break;
      case ButtonVariant.accent:
        backgroundColor =
            isEnabled ? AppColors.accent : AppColors.surfaceContainer;
        foregroundColor =
            isEnabled ? AppColors.textOnPrimary : AppColors.textDisabled;
        borderColor = null;
        break;
      case ButtonVariant.outline:
        backgroundColor = Colors.transparent;
        foregroundColor =
            isEnabled ? AppColors.primary : AppColors.textDisabled;
        borderColor = isEnabled ? AppColors.primary : AppColors.outline;
        break;
      case ButtonVariant.ghost:
        backgroundColor = Colors.transparent;
        foregroundColor =
            isEnabled ? AppColors.primary : AppColors.textDisabled;
        borderColor = null;
        break;
      case ButtonVariant.destructive:
        backgroundColor =
            isEnabled ? AppColors.error : AppColors.surfaceContainer;
        foregroundColor =
            isEnabled ? AppColors.textOnPrimary : AppColors.textDisabled;
        borderColor = null;
        break;
    }

    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: 0,
      shadowColor: AppColors.shadowMedium,
      side: borderColor != null
          ? BorderSide(color: borderColor, width: 1.5)
          : null,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      padding: padding,
      minimumSize: Size(0, height),
      textStyle: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.w600,
        fontFamily: 'Poppins',
      ),
    );
  }

  Widget _buildButtonContent() {
    if (widget.isLoading) {
      return SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.variant == ButtonVariant.outline ||
                    widget.variant == ButtonVariant.ghost
                ? AppColors.primary
                : AppColors.textOnPrimary,
          ),
        ),
      );
    }

    final List<Widget> children = [];

    if (widget.icon != null) {
      children.add(
          Icon(widget.icon, size: widget.size == ButtonSize.small ? 16 : 20));
      children.add(const SizedBox(width: 8));
    }

    children.add(
      Text(
        widget.text,
        style: TextStyle(
          fontSize: widget.size == ButtonSize.small
              ? 14
              : (widget.size == ButtonSize.large ? 18 : 16),
          fontWeight: FontWeight.w600,
          fontFamily: 'Poppins',
        ),
      ),
    );

    if (widget.suffixIcon != null) {
      children.add(const SizedBox(width: 8));
      children.add(Icon(widget.suffixIcon,
          size: widget.size == ButtonSize.small ? 16 : 20));
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled =
        !widget.disabled && !widget.isLoading && widget.onPressed != null;

    Widget button = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: ElevatedButton(
            onPressed: isEnabled ? widget.onPressed : null,
            style: _getButtonStyle(),
            child: _buildButtonContent(),
          ),
        );
      },
    );

    if (widget.fullWidth) {
      button = SizedBox(
        width: double.infinity,
        child: button,
      );
    } else if (widget.width != null) {
      button = SizedBox(
        width: widget.width,
        child: button,
      );
    }

    return GestureDetector(
      onTapDown: isEnabled ? (_) => _animationController.forward() : null,
      onTapUp: isEnabled ? (_) => _animationController.reverse() : null,
      onTapCancel: isEnabled ? () => _animationController.reverse() : null,
      child: button,
    );
  }
}
