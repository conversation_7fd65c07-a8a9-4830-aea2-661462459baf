class Mood {
  final String id;
  final String userId;
  final DateTime date;
  final int moodLevel;
  final String moodType;
  final List<String> emotions;
  final List<String> triggers;
  final List<String> activities;
  final double? sleepHours;
  final int? energyLevel;
  final int? stressLevel;
  final String? socialInteraction;
  final String? weather;
  final String? notes;
  final String? location;
  final bool isPrivate;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;

  Mood({
    required this.id,
    required this.userId,
    required this.date,
    required this.moodLevel,
    required this.moodType,
    required this.emotions,
    required this.triggers,
    required this.activities,
    this.sleepHours,
    this.energyLevel,
    this.stressLevel,
    this.socialInteraction,
    this.weather,
    this.notes,
    this.location,
    required this.isPrivate,
    required this.tags,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Mood.fromJson(Map<String, dynamic> json) {
    return Mood(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['user'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      moodLevel: json['moodLevel'] ?? 5,
      moodType: json['moodType'] ?? 'neutral',
      emotions: List<String>.from(json['emotions'] ?? []),
      triggers: List<String>.from(json['triggers'] ?? []),
      activities: List<String>.from(json['activities'] ?? []),
      sleepHours: json['sleepHours']?.toDouble(),
      energyLevel: json['energyLevel'],
      stressLevel: json['stressLevel'],
      socialInteraction: json['socialInteraction'],
      weather: json['weather'],
      notes: json['notes'],
      location: json['location'],
      isPrivate: json['isPrivate'] ?? false,
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user': userId,
      'date': date.toIso8601String(),
      'moodLevel': moodLevel,
      'moodType': moodType,
      'emotions': emotions,
      'triggers': triggers,
      'activities': activities,
      'sleepHours': sleepHours,
      'energyLevel': energyLevel,
      'stressLevel': stressLevel,
      'socialInteraction': socialInteraction,
      'weather': weather,
      'notes': notes,
      'location': location,
      'isPrivate': isPrivate,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  String get moodDescription {
    switch (moodLevel) {
      case 1:
        return 'Extremely Low';
      case 2:
        return 'Very Low';
      case 3:
        return 'Low';
      case 4:
        return 'Below Average';
      case 5:
        return 'Average';
      case 6:
        return 'Above Average';
      case 7:
        return 'Good';
      case 8:
        return 'Very Good';
      case 9:
        return 'Excellent';
      case 10:
        return 'Outstanding';
      default:
        return 'Unknown';
    }
  }

  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  Mood copyWith({
    String? id,
    String? userId,
    DateTime? date,
    int? moodLevel,
    String? moodType,
    List<String>? emotions,
    List<String>? triggers,
    List<String>? activities,
    double? sleepHours,
    int? energyLevel,
    int? stressLevel,
    String? socialInteraction,
    String? weather,
    String? notes,
    String? location,
    bool? isPrivate,
    List<String>? tags,
  }) {
    return Mood(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      moodLevel: moodLevel ?? this.moodLevel,
      moodType: moodType ?? this.moodType,
      emotions: emotions ?? this.emotions,
      triggers: triggers ?? this.triggers,
      activities: activities ?? this.activities,
      sleepHours: sleepHours ?? this.sleepHours,
      energyLevel: energyLevel ?? this.energyLevel,
      stressLevel: stressLevel ?? this.stressLevel,
      socialInteraction: socialInteraction ?? this.socialInteraction,
      weather: weather ?? this.weather,
      notes: notes ?? this.notes,
      location: location ?? this.location,
      isPrivate: isPrivate ?? this.isPrivate,
      tags: tags ?? this.tags,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }
}
