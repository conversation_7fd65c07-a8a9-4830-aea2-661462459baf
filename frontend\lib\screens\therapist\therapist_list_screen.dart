import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../providers/therapist_provider.dart';
import '../../utils/app_colors.dart';

class TherapistListScreen extends StatefulWidget {
  const TherapistListScreen({super.key});

  @override
  State<TherapistListScreen> createState() => _TherapistListScreenState();
}

class _TherapistListScreenState extends State<TherapistListScreen> {
  String _selectedSpecialization = 'all';
  String _selectedSessionType = 'all';
  double _maxDistance = 50.0;
  double _minRating = 0.0;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  final List<String> _specializations = [
    'all',
    'anxiety',
    'depression',
    'trauma',
    'ptsd',
    'addiction',
    'relationships',
    'family_therapy',
    'couples_therapy',
    'child_therapy',
    'adolescent_therapy',
    'grief_counseling',
    'eating_disorders',
    'bipolar_disorder',
    'ocd',
    'adhd',
    'autism_spectrum',
    'anger_management',
    'stress_management',
    'life_transitions',
    'career_counseling',
    'lgbtq_issues'
  ];

  final Map<String, String> _specializationLabels = {
    'all': 'All Specializations',
    'anxiety': 'Anxiety',
    'depression': 'Depression',
    'trauma': 'Trauma',
    'ptsd': 'PTSD',
    'addiction': 'Addiction',
    'relationships': 'Relationships',
    'family_therapy': 'Family Therapy',
    'couples_therapy': 'Couples Therapy',
    'child_therapy': 'Child Therapy',
    'adolescent_therapy': 'Adolescent Therapy',
    'grief_counseling': 'Grief Counseling',
    'eating_disorders': 'Eating Disorders',
    'bipolar_disorder': 'Bipolar Disorder',
    'ocd': 'OCD',
    'adhd': 'ADHD',
    'autism_spectrum': 'Autism Spectrum',
    'anger_management': 'Anger Management',
    'stress_management': 'Stress Management',
    'life_transitions': 'Life Transitions',
    'career_counseling': 'Career Counseling',
    'lgbtq_issues': 'LGBTQ+ Issues',
  };

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTherapists();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadTherapists() {
    final therapistProvider =
        Provider.of<TherapistProvider>(context, listen: false);
    therapistProvider.getTherapists();
  }

  List<Map<String, dynamic>> _getFilteredTherapists(
      List<Map<String, dynamic>> therapists) {
    return therapists.where((therapist) {
      final matchesSpecialization = _selectedSpecialization == 'all' ||
          (therapist['specializations'] as List?)
                  ?.contains(_selectedSpecialization) ==
              true;

      final matchesSessionType = _selectedSessionType == 'all' ||
          (therapist['sessionTypes'] as List?)
                  ?.contains(_selectedSessionType) ==
              true;

      final rating = (therapist['ratings']?['average'] ?? 0.0).toDouble();
      final matchesRating = rating >= _minRating;

      final matchesSearch = _searchQuery.isEmpty ||
          therapist['firstName']
              .toString()
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          therapist['lastName']
              .toString()
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          therapist['bio']
                  ?.toString()
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ==
              true;

      return matchesSpecialization &&
          matchesSessionType &&
          matchesRating &&
          matchesSearch;
    }).toList();
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _FilterBottomSheet(
        selectedSpecialization: _selectedSpecialization,
        selectedSessionType: _selectedSessionType,
        maxDistance: _maxDistance,
        minRating: _minRating,
        onApplyFilters: (specialization, sessionType, distance, rating) {
          setState(() {
            _selectedSpecialization = specialization;
            _selectedSessionType = sessionType;
            _maxDistance = distance;
            _minRating = rating;
          });
        },
        specializationLabels: _specializationLabels,
        specializations: _specializations,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Find Therapists'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Consumer<TherapistProvider>(
        builder: (context, therapistProvider, child) {
          if (therapistProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (therapistProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading therapists',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    therapistProvider.error!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadTherapists,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final filteredTherapists =
              _getFilteredTherapists(therapistProvider.therapists);

          return Column(
            children: [
              // Search Bar
              Container(
                padding: const EdgeInsets.all(16),
                color: AppColors.surfaceVariant,
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search therapists...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),

              // Active Filters
              if (_selectedSpecialization != 'all' ||
                  _selectedSessionType != 'all' ||
                  _minRating > 0) ...[
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        if (_selectedSpecialization != 'all')
                          _FilterChip(
                            label:
                                _specializationLabels[_selectedSpecialization]!,
                            onDeleted: () {
                              setState(() {
                                _selectedSpecialization = 'all';
                              });
                            },
                          ),
                        if (_selectedSessionType != 'all')
                          _FilterChip(
                            label: _selectedSessionType
                                .replaceAll('_', ' ')
                                .toUpperCase(),
                            onDeleted: () {
                              setState(() {
                                _selectedSessionType = 'all';
                              });
                            },
                          ),
                        if (_minRating > 0)
                          _FilterChip(
                            label: '${_minRating.toStringAsFixed(1)}+ Stars',
                            onDeleted: () {
                              setState(() {
                                _minRating = 0.0;
                              });
                            },
                          ),
                      ],
                    ),
                  ),
                ),
              ],

              // Therapist List
              Expanded(
                child: filteredTherapists.isEmpty
                    ? _buildEmptyState()
                    : RefreshIndicator(
                        onRefresh: () async => _loadTherapists(),
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: filteredTherapists.length,
                          itemBuilder: (context, index) {
                            final therapist = filteredTherapists[index];
                            return _TherapistCard(
                              therapist: therapist,
                              onTap: () {
                                context.go(
                                    '/therapist/${therapist['_id'] ?? therapist['id']}');
                              },
                            );
                          },
                        ),
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.psychology_outlined,
            size: 64,
            color: AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty ||
                    _selectedSpecialization != 'all' ||
                    _selectedSessionType != 'all'
                ? 'No therapists found'
                : 'No therapists available',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty ||
                    _selectedSpecialization != 'all' ||
                    _selectedSessionType != 'all'
                ? 'Try adjusting your search or filters'
                : 'Check back later for available therapists',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _FilterChip extends StatelessWidget {
  final String label;
  final VoidCallback onDeleted;

  const _FilterChip({
    required this.label,
    required this.onDeleted,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(label),
        deleteIcon: const Icon(Icons.close, size: 16),
        onDeleted: onDeleted,
        backgroundColor: AppColors.primary.withOpacity(0.1),
        deleteIconColor: AppColors.primary,
        labelStyle: const TextStyle(
          color: AppColors.primary,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

class _TherapistCard extends StatelessWidget {
  final Map<String, dynamic> therapist;
  final VoidCallback onTap;

  const _TherapistCard({
    required this.therapist,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final firstName = therapist['firstName'] ?? '';
    final lastName = therapist['lastName'] ?? '';
    final fullName = '$firstName $lastName';
    final bio = therapist['bio'] ?? '';
    final specializations =
        List<String>.from(therapist['specializations'] ?? []);
    final sessionTypes = List<String>.from(therapist['sessionTypes'] ?? []);
    final rating = (therapist['ratings']?['average'] ?? 0.0).toDouble();
    final reviewCount = therapist['ratings']?['count'] ?? 0;
    final sessionFee = therapist['pricing']?['sessionFee'] ?? 0;
    final isAcceptingClients = therapist['isAcceptingNewClients'] ?? false;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: AppColors.primary,
                    child: Text(
                      firstName.isNotEmpty ? firstName[0].toUpperCase() : 'T',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          fullName,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: rating > 0
                                  ? Colors.amber
                                  : AppColors.textTertiary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              rating > 0
                                  ? '${rating.toStringAsFixed(1)} ($reviewCount)'
                                  : 'No reviews',
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '\$${sessionFee.toStringAsFixed(0)}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                      const Text(
                        'per session',
                        style: TextStyle(
                          fontSize: 10,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Bio
              if (bio.isNotEmpty) ...[
                Text(
                  bio.length > 120 ? '${bio.substring(0, 120)}...' : bio,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                    height: 1.4,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
              ],

              // Specializations
              if (specializations.isNotEmpty) ...[
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: specializations.take(3).map((specialization) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.secondary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        specialization.replaceAll('_', ' ').toUpperCase(),
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: AppColors.secondary,
                        ),
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 12),
              ],

              // Session Types and Availability
              Row(
                children: [
                  if (sessionTypes.isNotEmpty) ...[
                    Icon(
                      sessionTypes.contains('video_call')
                          ? Icons.videocam
                          : Icons.person,
                      size: 16,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      sessionTypes.join(', ').replaceAll('_', ' '),
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                  const Spacer(),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: isAcceptingClients
                          ? AppColors.success
                          : AppColors.error,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      isAcceptingClients
                          ? 'ACCEPTING CLIENTS'
                          : 'NOT AVAILABLE',
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _FilterBottomSheet extends StatefulWidget {
  final String selectedSpecialization;
  final String selectedSessionType;
  final double maxDistance;
  final double minRating;
  final Function(String, String, double, double) onApplyFilters;
  final Map<String, String> specializationLabels;
  final List<String> specializations;

  const _FilterBottomSheet({
    required this.selectedSpecialization,
    required this.selectedSessionType,
    required this.maxDistance,
    required this.minRating,
    required this.onApplyFilters,
    required this.specializationLabels,
    required this.specializations,
  });

  @override
  State<_FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<_FilterBottomSheet> {
  late String _selectedSpecialization;
  late String _selectedSessionType;
  late double _maxDistance;
  late double _minRating;

  @override
  void initState() {
    super.initState();
    _selectedSpecialization = widget.selectedSpecialization;
    _selectedSessionType = widget.selectedSessionType;
    _maxDistance = widget.maxDistance;
    _minRating = widget.minRating;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Filter Therapists',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Specialization
          const Text(
            'Specialization',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedSpecialization,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            items: widget.specializations.map((specialization) {
              return DropdownMenuItem(
                value: specialization,
                child: Text(widget.specializationLabels[specialization] ??
                    specialization),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedSpecialization = value!;
              });
            },
          ),

          const SizedBox(height: 16),

          // Session Type
          const Text(
            'Session Type',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedSessionType,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            items: const [
              DropdownMenuItem(value: 'all', child: Text('All Types')),
              DropdownMenuItem(value: 'in_person', child: Text('In Person')),
              DropdownMenuItem(value: 'video_call', child: Text('Video Call')),
              DropdownMenuItem(value: 'phone_call', child: Text('Phone Call')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedSessionType = value!;
              });
            },
          ),

          const SizedBox(height: 16),

          // Minimum Rating
          Text(
            'Minimum Rating: ${_minRating.toStringAsFixed(1)} stars',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          Slider(
            value: _minRating,
            min: 0.0,
            max: 5.0,
            divisions: 10,
            onChanged: (value) {
              setState(() {
                _minRating = value;
              });
            },
          ),

          const SizedBox(height: 20),

          // Apply Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                widget.onApplyFilters(
                  _selectedSpecialization,
                  _selectedSessionType,
                  _maxDistance,
                  _minRating,
                );
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Apply Filters',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
