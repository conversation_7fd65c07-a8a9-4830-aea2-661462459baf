import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/api_service.dart';
import '../models/user_model.dart';

class AuthProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();

  User? _user;
  String? _token;
  bool _isLoading = true;
  bool _isAuthenticated = false;

  // Getters
  User? get user => _user;
  String? get token => _token;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;

  AuthProvider() {
    _loadStoredAuth();
  }

  // Load stored authentication data
  Future<void> _loadStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _token = prefs.getString('auth_token');

      if (_token != null) {
        print('AuthProvider: Found stored token, setting in API service');
        // Set token in API service before making request
        _apiService.setAuthToken(_token!);

        // Verify token and get user data
        final response = await _apiService.get('/auth/me');
        if (response['success']) {
          _user = User.fromJson(response['user']);
          _isAuthenticated = true;
          print('AuthProvider: Stored auth loaded successfully');
        } else {
          // Token is invalid, clear it
          print('AuthProvider: Stored token invalid, clearing auth');
          await _clearAuth();
        }
      } else {
        print('AuthProvider: No stored token found');
      }
    } catch (e) {
      debugPrint('Error loading stored auth: $e');
      await _clearAuth();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Register user
  Future<Map<String, dynamic>> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required DateTime dateOfBirth,
    required String gender,
    String? phoneNumber,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await _apiService.post('/auth/register', {
        'firstName': firstName,
        'lastName': lastName,
        'email': email,
        'password': password,
        'dateOfBirth': dateOfBirth.toIso8601String(),
        'gender': gender,
        'phoneNumber': phoneNumber,
      });

      if (response['success']) {
        _token = response['token'];
        _user = User.fromJson(response['user']);
        _isAuthenticated = true;

        // Store token
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('auth_token', _token!);

        // Set token in API service
        _apiService.setAuthToken(_token!);
      }

      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Registration failed: $e',
      };
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Login user
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
    String userType = 'user',
  }) async {
    try {
      print('AuthProvider: Starting login process');
      _isLoading = true;
      notifyListeners();

      print('AuthProvider: Making API call to /auth/login');
      final response = await _apiService.post('/auth/login', {
        'email': email,
        'password': password,
        'userType': userType,
      });

      print('AuthProvider: API response received: $response');

      if (response['success']) {
        print('AuthProvider: Login successful, setting auth state');
        _token = response['token'];
        _user = User.fromJson(response['user']);
        _isAuthenticated = true;

        print('AuthProvider: Token set: ${_token?.substring(0, 20)}...');
        print('AuthProvider: User set: ${_user?.email}');
        print('AuthProvider: isAuthenticated: $_isAuthenticated');

        // Store token
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('auth_token', _token!);
        print('AuthProvider: Token stored in SharedPreferences');

        // Set token in API service
        _apiService.setAuthToken(_token!);
        print('AuthProvider: Token set in API service');
      } else {
        print('AuthProvider: Login failed - ${response['message']}');
      }

      return response;
    } catch (e) {
      print('AuthProvider: Login error: $e');
      return {
        'success': false,
        'message': 'Login failed: $e',
      };
    } finally {
      _isLoading = false;
      print(
          'AuthProvider: Login process completed, isAuthenticated: $_isAuthenticated');
      notifyListeners();
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      // Call logout endpoint
      await _apiService.post('/auth/logout', {});
    } catch (e) {
      debugPrint('Error during logout: $e');
    } finally {
      await _clearAuth();
    }
  }

  // Clear authentication data
  Future<void> _clearAuth() async {
    _user = null;
    _token = null;
    _isAuthenticated = false;

    // Clear stored token
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');

    // Clear token from API service
    _apiService.clearAuthToken();

    notifyListeners();
  }

  // Update user profile
  Future<Map<String, dynamic>> updateProfile(Map<String, dynamic> data) async {
    try {
      final response = await _apiService.put('/users/profile', data);

      if (response['success']) {
        _user = User.fromJson(response['data']);
        notifyListeners();
      }

      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Profile update failed: $e',
      };
    }
  }

  // Change password
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await _apiService.put('/users/change-password', {
        'currentPassword': currentPassword,
        'newPassword': newPassword,
      });

      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Password change failed: $e',
      };
    }
  }

  // Forgot password
  Future<Map<String, dynamic>> forgotPassword({
    required String email,
    String userType = 'user',
  }) async {
    try {
      final response = await _apiService.post('/auth/forgot-password', {
        'email': email,
        'userType': userType,
      });

      return response;
    } catch (e) {
      return {
        'success': false,
        'message': 'Password reset failed: $e',
      };
    }
  }
}
