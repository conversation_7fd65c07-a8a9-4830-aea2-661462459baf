import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../models/location_model.dart';
import '../services/api_service.dart';

class LocationProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  Location? _currentLocation;
  List<NearbyTherapist> _nearbyTherapists = [];
  bool _isLoading = false;
  bool _isLocationEnabled = false;
  String? _error;

  Location? get currentLocation => _currentLocation;
  List<NearbyTherapist> get nearbyTherapists => _nearbyTherapists;
  bool get isLoading => _isLoading;
  bool get isLocationEnabled => _isLocationEnabled;
  String? get error => _error;

  // Initialize location services
  Future<void> initializeLocation() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _error = 'Location services are disabled. Please enable location services.';
        _isLocationEnabled = false;
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _error = 'Location permissions are denied. Please grant location access.';
          _isLocationEnabled = false;
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _error = 'Location permissions are permanently denied. Please enable in settings.';
        _isLocationEnabled = false;
        return;
      }

      _isLocationEnabled = true;
      await getCurrentLocation();
    } catch (e) {
      _error = 'Error initializing location: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get current user location
  Future<void> getCurrentLocation() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      Placemark? placemark = placemarks.isNotEmpty ? placemarks.first : null;

      _currentLocation = Location(
        latitude: position.latitude,
        longitude: position.longitude,
        address: placemark?.street,
        city: placemark?.locality,
        state: placemark?.administrativeArea,
        country: placemark?.country,
        postalCode: placemark?.postalCode,
      );

      // Automatically search for nearby therapists
      await searchNearbyTherapists();
    } catch (e) {
      _error = 'Error getting current location: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Search for nearby therapists
  Future<void> searchNearbyTherapists({
    double radiusKm = 50.0,
    List<String>? specializations,
    String? language,
    double? minRating,
    double? maxHourlyRate,
    bool? onlineOnly,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      if (_currentLocation == null) {
        _error = 'Location not available. Please enable location services.';
        return;
      }

      final queryParams = {
        'latitude': _currentLocation!.latitude.toString(),
        'longitude': _currentLocation!.longitude.toString(),
        'radius': radiusKm.toString(),
        if (specializations != null && specializations.isNotEmpty)
          'specializations': specializations.join(','),
        if (language != null) 'language': language,
        if (minRating != null) 'minRating': minRating.toString(),
        if (maxHourlyRate != null) 'maxHourlyRate': maxHourlyRate.toString(),
        if (onlineOnly != null) 'onlineOnly': onlineOnly.toString(),
      };

      final response = await _apiService.get('/therapists/nearby', queryParams: queryParams);
      
      if (response['success']) {
        _nearbyTherapists = (response['therapists'] as List)
            .map((json) => NearbyTherapist.fromJson(json))
            .toList();
      } else {
        _error = response['message'] ?? 'Failed to search nearby therapists';
      }
    } catch (e) {
      _error = 'Error searching nearby therapists: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get address from coordinates
  Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark placemark = placemarks.first;
        return '${placemark.street}, ${placemark.locality}, ${placemark.administrativeArea}';
      }
    } catch (e) {
      debugPrint('Error getting address: $e');
    }
    return null;
  }

  // Get coordinates from address
  Future<Location?> getLocationFromAddress(String address) async {
    try {
      List<Location> locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        Location location = locations.first;
        return Location(
          latitude: location.latitude,
          longitude: location.longitude,
          address: address,
        );
      }
    } catch (e) {
      debugPrint('Error getting location from address: $e');
    }
    return null;
  }

  // Filter therapists by criteria
  void filterTherapists({
    List<String>? specializations,
    String? language,
    double? minRating,
    double? maxHourlyRate,
    double? maxDistance,
    bool? onlineOnly,
    bool? inPersonOnly,
  }) {
    List<NearbyTherapist> filtered = List.from(_nearbyTherapists);

    if (specializations != null && specializations.isNotEmpty) {
      filtered = filtered.where((therapist) {
        return specializations.any((spec) => 
          therapist.specializations.any((tSpec) => 
            tSpec.toLowerCase().contains(spec.toLowerCase())
          )
        );
      }).toList();
    }

    if (language != null && language.isNotEmpty) {
      filtered = filtered.where((therapist) {
        return therapist.languages.any((lang) => 
          lang.toLowerCase().contains(language.toLowerCase())
        );
      }).toList();
    }

    if (minRating != null) {
      filtered = filtered.where((therapist) => therapist.rating >= minRating).toList();
    }

    if (maxHourlyRate != null) {
      filtered = filtered.where((therapist) => therapist.hourlyRate <= maxHourlyRate).toList();
    }

    if (maxDistance != null) {
      filtered = filtered.where((therapist) => therapist.distance <= maxDistance).toList();
    }

    if (onlineOnly == true) {
      filtered = filtered.where((therapist) => therapist.location.isOnlineAvailable).toList();
    }

    if (inPersonOnly == true) {
      filtered = filtered.where((therapist) => therapist.location.isInPersonAvailable).toList();
    }

    // Sort by distance
    filtered.sort((a, b) => a.distance.compareTo(b.distance));

    _nearbyTherapists = filtered;
    notifyListeners();
  }

  // Sort therapists
  void sortTherapists(String sortBy) {
    switch (sortBy) {
      case 'distance':
        _nearbyTherapists.sort((a, b) => a.distance.compareTo(b.distance));
        break;
      case 'rating':
        _nearbyTherapists.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'price_low':
        _nearbyTherapists.sort((a, b) => a.hourlyRate.compareTo(b.hourlyRate));
        break;
      case 'price_high':
        _nearbyTherapists.sort((a, b) => b.hourlyRate.compareTo(a.hourlyRate));
        break;
      case 'reviews':
        _nearbyTherapists.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
        break;
    }
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Set custom location
  void setCustomLocation(Location location) {
    _currentLocation = location;
    notifyListeners();
  }

  // Check if location is within service area
  bool isLocationInServiceArea(Location location) {
    // Add your service area logic here
    // For now, return true for all locations
    return true;
  }
}
