import 'package:flutter/foundation.dart';
import '../models/assessment_model.dart';
import '../services/api_service.dart';

class AssessmentProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  List<Assessment> _assessments = [];
  Assessment? _currentAssessment;
  bool _isLoading = false;
  String? _error;

  List<Assessment> get assessments => _assessments;
  Assessment? get currentAssessment => _currentAssessment;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get all assessments for user
  Future<void> fetchAssessments() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.get('/assessments');
      if (response['success']) {
        _assessments = (response['assessments'] as List)
            .map((json) => Assessment.fromJson(json))
            .toList();
      } else {
        _error = response['message'] ?? 'Failed to fetch assessments';
      }
    } catch (e) {
      _error = 'Error fetching assessments: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Start a new assessment
  Future<Assessment?> startAssessment(String type) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.post('/assessments/start', {
        'type': type,
      });

      if (response['success']) {
        _currentAssessment = Assessment.fromJson(response['assessment']);
        return _currentAssessment;
      } else {
        _error = response['message'] ?? 'Failed to start assessment';
        return null;
      }
    } catch (e) {
      _error = 'Error starting assessment: $e';
      debugPrint(_error);
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Submit assessment answers
  Future<AssessmentResult?> submitAssessment(String assessmentId, Map<String, int> answers) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.post('/assessments/$assessmentId/submit', {
        'answers': answers,
      });

      if (response['success']) {
        final result = AssessmentResult.fromJson(response['result']);
        
        // Update current assessment
        if (_currentAssessment?.id == assessmentId) {
          _currentAssessment = Assessment.fromJson(response['assessment']);
        }
        
        // Refresh assessments list
        await fetchAssessments();
        
        return result;
      } else {
        _error = response['message'] ?? 'Failed to submit assessment';
        return null;
      }
    } catch (e) {
      _error = 'Error submitting assessment: $e';
      debugPrint(_error);
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get assessment by ID
  Future<Assessment?> getAssessment(String assessmentId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.get('/assessments/$assessmentId');
      if (response['success']) {
        return Assessment.fromJson(response['assessment']);
      } else {
        _error = response['message'] ?? 'Failed to fetch assessment';
        return null;
      }
    } catch (e) {
      _error = 'Error fetching assessment: $e';
      debugPrint(_error);
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get predefined assessment templates
  List<Assessment> getAssessmentTemplates() {
    return [
      Assessment(
        id: 'phq9',
        userId: '',
        type: 'depression',
        title: 'PHQ-9 Depression Assessment',
        questions: _getPHQ9Questions(),
        createdAt: DateTime.now(),
        isCompleted: false,
      ),
      Assessment(
        id: 'gad7',
        userId: '',
        type: 'anxiety',
        title: 'GAD-7 Anxiety Assessment',
        questions: _getGAD7Questions(),
        createdAt: DateTime.now(),
        isCompleted: false,
      ),
      Assessment(
        id: 'stress',
        userId: '',
        type: 'stress',
        title: 'Perceived Stress Scale',
        questions: _getStressQuestions(),
        createdAt: DateTime.now(),
        isCompleted: false,
      ),
    ];
  }

  // PHQ-9 Depression Questions
  List<AssessmentQuestion> _getPHQ9Questions() {
    final options = [
      AssessmentOption(text: 'Not at all', value: 0),
      AssessmentOption(text: 'Several days', value: 1),
      AssessmentOption(text: 'More than half the days', value: 2),
      AssessmentOption(text: 'Nearly every day', value: 3),
    ];

    return [
      AssessmentQuestion(
        id: 'phq9_1',
        question: 'Little interest or pleasure in doing things',
        options: options,
      ),
      AssessmentQuestion(
        id: 'phq9_2',
        question: 'Feeling down, depressed, or hopeless',
        options: options,
      ),
      AssessmentQuestion(
        id: 'phq9_3',
        question: 'Trouble falling or staying asleep, or sleeping too much',
        options: options,
      ),
      AssessmentQuestion(
        id: 'phq9_4',
        question: 'Feeling tired or having little energy',
        options: options,
      ),
      AssessmentQuestion(
        id: 'phq9_5',
        question: 'Poor appetite or overeating',
        options: options,
      ),
      AssessmentQuestion(
        id: 'phq9_6',
        question: 'Feeling bad about yourself or that you are a failure',
        options: options,
      ),
      AssessmentQuestion(
        id: 'phq9_7',
        question: 'Trouble concentrating on things',
        options: options,
      ),
      AssessmentQuestion(
        id: 'phq9_8',
        question: 'Moving or speaking slowly, or being fidgety/restless',
        options: options,
      ),
      AssessmentQuestion(
        id: 'phq9_9',
        question: 'Thoughts that you would be better off dead',
        options: options,
      ),
    ];
  }

  // GAD-7 Anxiety Questions
  List<AssessmentQuestion> _getGAD7Questions() {
    final options = [
      AssessmentOption(text: 'Not at all', value: 0),
      AssessmentOption(text: 'Several days', value: 1),
      AssessmentOption(text: 'More than half the days', value: 2),
      AssessmentOption(text: 'Nearly every day', value: 3),
    ];

    return [
      AssessmentQuestion(
        id: 'gad7_1',
        question: 'Feeling nervous, anxious, or on edge',
        options: options,
      ),
      AssessmentQuestion(
        id: 'gad7_2',
        question: 'Not being able to stop or control worrying',
        options: options,
      ),
      AssessmentQuestion(
        id: 'gad7_3',
        question: 'Worrying too much about different things',
        options: options,
      ),
      AssessmentQuestion(
        id: 'gad7_4',
        question: 'Trouble relaxing',
        options: options,
      ),
      AssessmentQuestion(
        id: 'gad7_5',
        question: 'Being so restless that it is hard to sit still',
        options: options,
      ),
      AssessmentQuestion(
        id: 'gad7_6',
        question: 'Becoming easily annoyed or irritable',
        options: options,
      ),
      AssessmentQuestion(
        id: 'gad7_7',
        question: 'Feeling afraid, as if something awful might happen',
        options: options,
      ),
    ];
  }

  // Stress Assessment Questions
  List<AssessmentQuestion> _getStressQuestions() {
    final options = [
      AssessmentOption(text: 'Never', value: 0),
      AssessmentOption(text: 'Almost never', value: 1),
      AssessmentOption(text: 'Sometimes', value: 2),
      AssessmentOption(text: 'Fairly often', value: 3),
      AssessmentOption(text: 'Very often', value: 4),
    ];

    return [
      AssessmentQuestion(
        id: 'stress_1',
        question: 'How often have you been upset because of something unexpected?',
        options: options,
      ),
      AssessmentQuestion(
        id: 'stress_2',
        question: 'How often have you felt unable to control important things in your life?',
        options: options,
      ),
      AssessmentQuestion(
        id: 'stress_3',
        question: 'How often have you felt nervous and stressed?',
        options: options,
      ),
      AssessmentQuestion(
        id: 'stress_4',
        question: 'How often have you felt confident about handling personal problems?',
        options: options,
      ),
      AssessmentQuestion(
        id: 'stress_5',
        question: 'How often have you felt that things were going your way?',
        options: options,
      ),
    ];
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void setCurrentAssessment(Assessment? assessment) {
    _currentAssessment = assessment;
    notifyListeners();
  }
}
