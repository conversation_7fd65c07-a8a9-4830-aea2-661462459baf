const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Therapist = require('../models/Therapist');
const { generateToken, protect, sensitiveOperationLimit } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// Validation middleware
const validateRegistration = [
  body('firstName').trim().isLength({ min: 2, max: 50 }).withMessage('First name must be 2-50 characters'),
  body('lastName').trim().isLength({ min: 2, max: 50 }).withMessage('Last name must be 2-50 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('dateOfBirth').isISO8601().withMessage('Please provide a valid date of birth'),
  body('gender').isIn(['male', 'female', 'other', 'prefer_not_to_say']).withMessage('Please select a valid gender')
];

const validateLogin = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').notEmpty().withMessage('Password is required')
];

const validateTherapistRegistration = [
  ...validateRegistration,
  body('licenseNumber').trim().notEmpty().withMessage('License number is required'),
  body('licenseState').trim().notEmpty().withMessage('License state is required'),
  body('licenseExpiry').isISO8601().withMessage('Please provide a valid license expiry date'),
  body('yearsOfExperience').isInt({ min: 0 }).withMessage('Years of experience must be a positive number'),
  body('sessionFee').isFloat({ min: 0 }).withMessage('Session fee must be a positive number')
];

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
const registerUser = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const {
    firstName,
    lastName,
    email,
    password,
    dateOfBirth,
    gender,
    phoneNumber,
    location
  } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ email });
  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: 'User already exists with this email'
    });
  }

  // Create user
  const user = await User.create({
    firstName,
    lastName,
    email,
    password,
    dateOfBirth,
    gender,
    phoneNumber,
    location
  });

  // Generate token
  const token = generateToken(user._id, 'user');

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    token,
    user: {
      id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      fullName: user.fullName,
      age: user.age,
      gender: user.gender,
      isEmailVerified: user.isEmailVerified
    }
  });
});

// @desc    Register therapist
// @route   POST /api/auth/register/therapist
// @access  Public
const registerTherapist = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const {
    firstName,
    lastName,
    email,
    password,
    dateOfBirth,
    gender,
    phoneNumber,
    licenseNumber,
    licenseState,
    licenseExpiry,
    credentials,
    specializations,
    therapyApproaches,
    yearsOfExperience,
    education,
    practiceType,
    practiceName,
    location,
    languages,
    sessionTypes,
    sessionFee,
    bio
  } = req.body;

  // Check if therapist already exists
  const existingTherapist = await Therapist.findOne({
    $or: [{ email }, { licenseNumber }]
  });
  
  if (existingTherapist) {
    const field = existingTherapist.email === email ? 'email' : 'license number';
    return res.status(400).json({
      success: false,
      message: `Therapist already exists with this ${field}`
    });
  }

  // Create therapist
  const therapist = await Therapist.create({
    firstName,
    lastName,
    email,
    password,
    dateOfBirth,
    gender,
    phoneNumber,
    licenseNumber,
    licenseState,
    licenseExpiry,
    credentials,
    specializations,
    therapyApproaches,
    yearsOfExperience,
    education,
    practiceType,
    practiceName,
    location,
    languages,
    sessionTypes,
    pricing: {
      sessionFee,
      currency: 'USD'
    },
    bio
  });

  // Generate token
  const token = generateToken(therapist._id, 'therapist');

  res.status(201).json({
    success: true,
    message: 'Therapist registered successfully. Account pending verification.',
    token,
    therapist: {
      id: therapist._id,
      firstName: therapist.firstName,
      lastName: therapist.lastName,
      email: therapist.email,
      fullName: therapist.fullName,
      licenseNumber: therapist.licenseNumber,
      specializations: therapist.specializations,
      isVerified: therapist.isVerified,
      isActive: therapist.isActive
    }
  });
});

// @desc    Login user/therapist
// @route   POST /api/auth/login
// @access  Public
const login = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const { email, password, userType = 'user' } = req.body;

  // Find user based on type
  let user;
  let Model = userType === 'therapist' ? Therapist : User;
  
  user = await Model.findOne({ email }).select('+password');

  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }

  // Check if account is active
  if (!user.isActive) {
    return res.status(401).json({
      success: false,
      message: 'Account is deactivated'
    });
  }

  // Check password
  const isPasswordMatch = await user.comparePassword(password);
  if (!isPasswordMatch) {
    return res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }

  // Update last login
  user.lastLogin = new Date();
  await user.save({ validateBeforeSave: false });

  // Generate token
  const token = generateToken(user._id, userType);

  // Prepare response data
  const responseData = {
    id: user._id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    fullName: user.fullName,
    userType
  };

  if (userType === 'therapist') {
    responseData.licenseNumber = user.licenseNumber;
    responseData.specializations = user.specializations;
    responseData.isVerified = user.isVerified;
  } else {
    responseData.age = user.age;
    responseData.gender = user.gender;
    responseData.isEmailVerified = user.isEmailVerified;
  }

  res.status(200).json({
    success: true,
    message: 'Login successful',
    token,
    user: responseData
  });
});

// @desc    Get current user
// @route   GET /api/auth/me
// @access  Private
const getMe = asyncHandler(async (req, res) => {
  const user = req.user;
  const userType = req.userRole;

  const responseData = {
    id: user._id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    fullName: user.fullName,
    userType,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt
  };

  if (userType === 'therapist') {
    responseData.licenseNumber = user.licenseNumber;
    responseData.specializations = user.specializations;
    responseData.isVerified = user.isVerified;
    responseData.ratings = user.ratings;
    responseData.totalSessions = user.totalSessions;
  } else {
    responseData.age = user.age;
    responseData.gender = user.gender;
    responseData.isEmailVerified = user.isEmailVerified;
    responseData.subscription = user.subscription;
  }

  res.status(200).json({
    success: true,
    user: responseData
  });
});

// @desc    Logout user
// @route   POST /api/auth/logout
// @access  Private
const logout = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Logged out successfully'
  });
});

// @desc    Forgot password
// @route   POST /api/auth/forgot-password
// @access  Public
const forgotPassword = asyncHandler(async (req, res) => {
  const { email, userType = 'user' } = req.body;

  const Model = userType === 'therapist' ? Therapist : User;
  const user = await Model.findOne({ email });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'No account found with this email'
    });
  }

  // Generate reset token
  const resetToken = user.generatePasswordResetToken();
  await user.save({ validateBeforeSave: false });

  // In a real application, you would send an email here
  // For now, we'll just return the token (remove this in production)
  res.status(200).json({
    success: true,
    message: 'Password reset token generated',
    resetToken // Remove this in production
  });
});

// Routes
router.post('/register', validateRegistration, registerUser);
router.post('/register/therapist', validateTherapistRegistration, registerTherapist);
router.post('/login', validateLogin, sensitiveOperationLimit, login);
router.get('/me', protect, getMe);
router.post('/logout', protect, logout);
router.post('/forgot-password', sensitiveOperationLimit, forgotPassword);

module.exports = router;
