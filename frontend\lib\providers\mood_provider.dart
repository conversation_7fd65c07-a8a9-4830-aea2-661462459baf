import 'package:flutter/foundation.dart';
import '../services/api_service.dart';
import '../models/mood_model.dart';

class MoodProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  List<Mood> _moods = [];
  Mood? _todaysMood;
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic>? _moodStats;

  // Getters
  List<Mood> get moods => _moods;
  Mood? get todaysMood => _todaysMood;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, dynamic>? get moodStats => _moodStats;

  // Create mood entry
  Future<Map<String, dynamic>> createMoodEntry(Map<String, dynamic> moodData) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.post('/moods', moodData);
      
      if (response['success']) {
        final mood = Mood.fromJson(response['data']);
        _moods.insert(0, mood);
        _todaysMood = mood;
      }

      return response;
    } catch (e) {
      _error = e.toString();
      return {
        'success': false,
        'message': 'Failed to create mood entry: $e',
      };
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get mood entries
  Future<void> getMoodEntries({
    int page = 1,
    int limit = 20,
    String? startDate,
    String? endDate,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final params = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (startDate != null) params['startDate'] = startDate;
      if (endDate != null) params['endDate'] = endDate;

      final response = await _apiService.getWithParams('/moods', params);
      
      if (response['success']) {
        final moodList = (response['data'] as List)
            .map((json) => Mood.fromJson(json))
            .toList();
        
        if (page == 1) {
          _moods = moodList;
        } else {
          _moods.addAll(moodList);
        }
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get today's mood
  Future<void> getTodaysMood() async {
    try {
      final response = await _apiService.get('/moods/today');
      
      if (response['success'] && response['data'] != null) {
        _todaysMood = Mood.fromJson(response['data']);
      } else {
        _todaysMood = null;
      }
      
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  // Get mood statistics
  Future<void> getMoodStats({int days = 30}) async {
    try {
      final response = await _apiService.getWithParams('/moods/stats', {
        'period': days.toString(),
      });
      
      if (response['success']) {
        _moodStats = response['data'];
      }
      
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  // Update mood entry
  Future<Map<String, dynamic>> updateMoodEntry(String id, Map<String, dynamic> moodData) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.put('/moods/$id', moodData);
      
      if (response['success']) {
        final updatedMood = Mood.fromJson(response['data']);
        final index = _moods.indexWhere((mood) => mood.id == id);
        if (index != -1) {
          _moods[index] = updatedMood;
        }
        
        // Update today's mood if it's the same entry
        if (_todaysMood?.id == id) {
          _todaysMood = updatedMood;
        }
      }

      return response;
    } catch (e) {
      _error = e.toString();
      return {
        'success': false,
        'message': 'Failed to update mood entry: $e',
      };
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Delete mood entry
  Future<Map<String, dynamic>> deleteMoodEntry(String id) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.delete('/moods/$id');
      
      if (response['success']) {
        _moods.removeWhere((mood) => mood.id == id);
        
        // Clear today's mood if it's the same entry
        if (_todaysMood?.id == id) {
          _todaysMood = null;
        }
      }

      return response;
    } catch (e) {
      _error = e.toString();
      return {
        'success': false,
        'message': 'Failed to delete mood entry: $e',
      };
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Reset provider
  void reset() {
    _moods = [];
    _todaysMood = null;
    _moodStats = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }
}
