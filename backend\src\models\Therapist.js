const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const therapistSchema = new mongoose.Schema({
  // Personal Information
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false
  },
  phoneNumber: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true,
    match: [/^\+?[\d\s-()]+$/, 'Please enter a valid phone number']
  },
  profilePicture: {
    type: String,
    default: null
  },
  dateOfBirth: {
    type: Date,
    required: [true, 'Date of birth is required']
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other'],
    required: [true, 'Gender is required']
  },

  // Professional Information
  licenseNumber: {
    type: String,
    required: [true, 'License number is required'],
    unique: true,
    trim: true
  },
  licenseState: {
    type: String,
    required: [true, 'License state is required'],
    trim: true
  },
  licenseExpiry: {
    type: Date,
    required: [true, 'License expiry date is required']
  },
  credentials: [{
    type: String,
    enum: [
      'PhD', 'PsyD', 'LCSW', 'LMFT', 'LPC', 'LPCC', 'LMHC',
      'LCPC', 'LMSW', 'LSW', 'LISW', 'ACSW', 'DCSW'
    ]
  }],
  specializations: [{
    type: String,
    enum: [
      'anxiety', 'depression', 'trauma', 'ptsd', 'addiction',
      'relationships', 'family_therapy', 'couples_therapy',
      'child_therapy', 'adolescent_therapy', 'grief_counseling',
      'eating_disorders', 'bipolar_disorder', 'ocd', 'adhd',
      'autism_spectrum', 'anger_management', 'stress_management',
      'life_transitions', 'career_counseling', 'lgbtq_issues'
    ]
  }],
  therapyApproaches: [{
    type: String,
    enum: [
      'cognitive_behavioral_therapy', 'dialectical_behavior_therapy',
      'psychodynamic_therapy', 'humanistic_therapy', 'gestalt_therapy',
      'family_systems_therapy', 'solution_focused_therapy',
      'acceptance_commitment_therapy', 'mindfulness_based_therapy',
      'emdr', 'somatic_therapy', 'art_therapy', 'music_therapy'
    ]
  }],
  yearsOfExperience: {
    type: Number,
    required: [true, 'Years of experience is required'],
    min: [0, 'Years of experience cannot be negative']
  },
  education: [{
    degree: {
      type: String,
      required: true
    },
    institution: {
      type: String,
      required: true
    },
    graduationYear: {
      type: Number,
      required: true
    },
    fieldOfStudy: String
  }],

  // Practice Information
  practiceType: {
    type: String,
    enum: ['private_practice', 'group_practice', 'clinic', 'hospital', 'community_center'],
    required: [true, 'Practice type is required']
  },
  practiceName: {
    type: String,
    trim: true,
    maxlength: [100, 'Practice name cannot exceed 100 characters']
  },
  location: {
    address: {
      type: String,
      required: [true, 'Address is required']
    },
    city: {
      type: String,
      required: [true, 'City is required']
    },
    state: {
      type: String,
      required: [true, 'State is required']
    },
    zipCode: {
      type: String,
      required: [true, 'Zip code is required']
    },
    country: {
      type: String,
      default: 'United States'
    },
    coordinates: {
      latitude: {
        type: Number,
        required: [true, 'Latitude is required']
      },
      longitude: {
        type: Number,
        required: [true, 'Longitude is required']
      }
    }
  },
  languages: [{
    type: String,
    required: true
  }],
  sessionTypes: [{
    type: String,
    enum: ['in_person', 'video_call', 'phone_call'],
    required: true
  }],

  // Availability and Pricing
  availability: {
    monday: {
      isAvailable: { type: Boolean, default: false },
      timeSlots: [{
        startTime: String, // Format: "09:00"
        endTime: String    // Format: "17:00"
      }]
    },
    tuesday: {
      isAvailable: { type: Boolean, default: false },
      timeSlots: [{
        startTime: String,
        endTime: String
      }]
    },
    wednesday: {
      isAvailable: { type: Boolean, default: false },
      timeSlots: [{
        startTime: String,
        endTime: String
      }]
    },
    thursday: {
      isAvailable: { type: Boolean, default: false },
      timeSlots: [{
        startTime: String,
        endTime: String
      }]
    },
    friday: {
      isAvailable: { type: Boolean, default: false },
      timeSlots: [{
        startTime: String,
        endTime: String
      }]
    },
    saturday: {
      isAvailable: { type: Boolean, default: false },
      timeSlots: [{
        startTime: String,
        endTime: String
      }]
    },
    sunday: {
      isAvailable: { type: Boolean, default: false },
      timeSlots: [{
        startTime: String,
        endTime: String
      }]
    }
  },
  pricing: {
    sessionFee: {
      type: Number,
      required: [true, 'Session fee is required'],
      min: [0, 'Session fee cannot be negative']
    },
    currency: {
      type: String,
      default: 'USD'
    },
    acceptsInsurance: {
      type: Boolean,
      default: false
    },
    insuranceProviders: [String],
    slidingScale: {
      isAvailable: { type: Boolean, default: false },
      minFee: Number,
      maxFee: Number
    }
  },

  // Profile and Reviews
  bio: {
    type: String,
    maxlength: [1000, 'Bio cannot exceed 1000 characters']
  },
  personalStatement: {
    type: String,
    maxlength: [2000, 'Personal statement cannot exceed 2000 characters']
  },
  ratings: {
    average: {
      type: Number,
      default: 0,
      min: [0, 'Rating cannot be negative'],
      max: [5, 'Rating cannot exceed 5']
    },
    count: {
      type: Number,
      default: 0
    }
  },
  reviews: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    rating: {
      type: Number,
      required: true,
      min: [1, 'Rating must be between 1 and 5'],
      max: [5, 'Rating must be between 1 and 5']
    },
    comment: {
      type: String,
      maxlength: [500, 'Review comment cannot exceed 500 characters']
    },
    isAnonymous: {
      type: Boolean,
      default: false
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Account Status
  isVerified: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isAcceptingNewClients: {
    type: Boolean,
    default: true
  },
  verificationDocuments: [{
    type: {
      type: String,
      enum: ['license', 'diploma', 'certification', 'insurance'],
      required: true
    },
    url: {
      type: String,
      required: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    },
    isVerified: {
      type: Boolean,
      default: false
    }
  }],

  // Statistics
  totalSessions: {
    type: Number,
    default: 0
  },
  totalClients: {
    type: Number,
    default: 0
  },
  joinedAt: {
    type: Date,
    default: Date.now
  },
  lastLogin: Date,

  // Security
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  emailVerificationToken: String,
  emailVerificationExpire: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
therapistSchema.index({ 'location.coordinates': '2dsphere' });
therapistSchema.index({ specializations: 1 });
therapistSchema.index({ 'ratings.average': -1 });
therapistSchema.index({ isVerified: 1, isActive: 1, isAcceptingNewClients: 1 });

// Virtual for full name
therapistSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for age
therapistSchema.virtual('age').get(function() {
  if (!this.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
});

// Pre-save middleware to hash password
therapistSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12);
  this.password = await bcrypt.hash(this.password, salt);
  next();
});

// Method to compare password
therapistSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to calculate average rating
therapistSchema.methods.calculateAverageRating = function() {
  if (this.reviews.length === 0) {
    this.ratings.average = 0;
    this.ratings.count = 0;
    return;
  }
  
  const sum = this.reviews.reduce((acc, review) => acc + review.rating, 0);
  this.ratings.average = Math.round((sum / this.reviews.length) * 10) / 10;
  this.ratings.count = this.reviews.length;
};

module.exports = mongoose.model('Therapist', therapistSchema);
