import 'package:flutter/foundation.dart';
import '../services/api_service.dart';

class JournalProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  List<Map<String, dynamic>> _journals = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Map<String, dynamic>> get journals => _journals;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get journals
  Future<void> getJournals() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.get('/journals');
      
      if (response['success']) {
        _journals = List<Map<String, dynamic>>.from(response['data'] ?? []);
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Create journal entry
  Future<Map<String, dynamic>> createJournal(Map<String, dynamic> journalData) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.post('/journals', journalData);
      
      if (response['success']) {
        _journals.insert(0, response['data']);
      }

      return response;
    } catch (e) {
      _error = e.toString();
      return {
        'success': false,
        'message': 'Failed to create journal: $e',
      };
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
