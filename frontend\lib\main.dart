import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import 'providers/auth_provider.dart';
import 'providers/mood_provider.dart';
import 'providers/journal_provider.dart';
import 'providers/therapist_provider.dart';
import 'providers/assessment_provider.dart';
import 'providers/location_provider.dart';
import 'screens/splash_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/register_screen.dart';
import 'screens/home/<USER>';
import 'screens/mood/mood_tracking_screen.dart';
import 'screens/journal/journal_list_screen.dart';
import 'screens/journal/journal_entry_screen.dart';
import 'screens/therapist/therapist_list_screen.dart';
import 'screens/therapist/therapist_detail_screen.dart';
import 'screens/profile/profile_screen.dart';
import 'screens/assessment/assessment_screen.dart';
import 'screens/therapist/nearby_therapists_screen.dart';
import 'utils/app_theme.dart';

void main() {
  runApp(const MindEasyApp());
}

class MindEasyApp extends StatelessWidget {
  const MindEasyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => MoodProvider()),
        ChangeNotifierProvider(create: (_) => JournalProvider()),
        ChangeNotifierProvider(create: (_) => TherapistProvider()),
        ChangeNotifierProvider(create: (_) => AssessmentProvider()),
        ChangeNotifierProvider(create: (_) => LocationProvider()),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp.router(
            title: 'MindEasy',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            routerConfig: _createRouter(authProvider),
          );
        },
      ),
    );
  }

  GoRouter _createRouter(AuthProvider authProvider) {
    return GoRouter(
      initialLocation: '/splash',
      redirect: (context, state) {
        final isLoggedIn = authProvider.isAuthenticated;
        final isLoading = authProvider.isLoading;

        // Show splash screen while loading
        if (isLoading) {
          return '/splash';
        }

        // Redirect to home if logged in and trying to access auth pages
        if (isLoggedIn &&
            (state.fullPath == '/login' || state.fullPath == '/register')) {
          return '/home';
        }

        // Redirect to login if not logged in and trying to access protected pages
        if (!isLoggedIn &&
            state.fullPath != '/login' &&
            state.fullPath != '/register' &&
            state.fullPath != '/splash') {
          return '/login';
        }

        return null;
      },
      routes: [
        GoRoute(
          path: '/splash',
          builder: (context, state) => const SplashScreen(),
        ),
        GoRoute(
          path: '/login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/register',
          builder: (context, state) => const RegisterScreen(),
        ),
        GoRoute(
          path: '/home',
          builder: (context, state) => const HomeScreen(),
        ),
        GoRoute(
          path: '/mood-tracking',
          builder: (context, state) => const MoodTrackingScreen(),
        ),
        GoRoute(
          path: '/journals',
          builder: (context, state) => const JournalListScreen(),
        ),
        GoRoute(
          path: '/journal/new',
          builder: (context, state) => const JournalEntryScreen(),
        ),
        GoRoute(
          path: '/journal/:id',
          builder: (context, state) => JournalEntryScreen(
            journalId: state.pathParameters['id'],
          ),
        ),
        GoRoute(
          path: '/therapists',
          builder: (context, state) => const TherapistListScreen(),
        ),
        GoRoute(
          path: '/therapist/:id',
          builder: (context, state) => TherapistDetailScreen(
            therapistId: state.pathParameters['id']!,
          ),
        ),
        GoRoute(
          path: '/profile',
          builder: (context, state) => const ProfileScreen(),
        ),
        GoRoute(
          path: '/assessments',
          builder: (context, state) => const AssessmentScreen(),
        ),
        GoRoute(
          path: '/nearby-therapists',
          builder: (context, state) => const NearbyTherapistsScreen(),
        ),
      ],
    );
  }
}
