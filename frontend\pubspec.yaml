name: mindeasy
description: AI-powered mental health application

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # State Management
  provider: ^6.0.5
  
  # HTTP requests
  http: ^1.1.0
  dio: ^5.3.2
  
  # Local storage
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  
  # UI Components
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.7
  cached_network_image: ^3.3.0
  
  # Charts and Analytics
  fl_chart: ^0.64.0
  
  # Date and Time
  intl: ^0.19.0

  # Navigation
  go_router: ^12.1.1

  # Forms and Validation
  flutter_form_builder: ^9.1.1
  form_builder_validators: ^11.1.0
  
  # Location services
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  
  # Maps
  google_maps_flutter: ^2.5.0
  
  # Image handling
  image_picker: ^1.0.4
  
  # Animations
  lottie: ^2.7.0
  
  # Utils
  uuid: ^4.1.0
  url_launcher: ^6.2.1
  
  # Notifications
  flutter_local_notifications: ^16.1.0
  
  # Video calling (for consultations)
  agora_rtc_engine: ^6.3.0
  
  # Payment processing
  flutter_stripe: ^9.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
