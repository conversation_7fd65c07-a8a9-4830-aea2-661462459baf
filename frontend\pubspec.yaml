name: mindeasy
description: AI-powered mental health application

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # State Management
  provider: ^6.0.5

  # HTTP requests
  http: ^1.1.0

  # Local storage
  shared_preferences: ^2.2.2

  # UI Components
  cupertino_icons: ^1.0.2
  google_fonts: ^6.1.0

  # Charts and Analytics
  fl_chart: ^0.64.0

  # Date and Time
  intl: any

  # Navigation
  go_router: ^12.1.1

  # Utils
  uuid: ^4.1.0

  # Geolocation and Maps
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  google_maps_flutter: ^2.5.3

  # Payment Integration
  flutter_stripe: ^10.1.1

  # Video Calling
  agora_rtc_engine: ^6.3.2
  permission_handler: ^11.3.1

  # Additional UI Components
  flutter_rating_bar: ^4.0.1
  image_picker: ^1.0.7
  cached_network_image: ^3.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
