import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../providers/mood_provider.dart';
import '../../utils/app_colors.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';

class MoodTrackingScreen extends StatefulWidget {
  const MoodTrackingScreen({super.key});

  @override
  State<MoodTrackingScreen> createState() => _MoodTrackingScreenState();
}

class _MoodTrackingScreenState extends State<MoodTrackingScreen> {
  int _moodLevel = 5;
  String _moodType = 'neutral';
  final List<String> _selectedEmotions = [];
  final List<String> _selectedActivities = [];
  final _notesController = TextEditingController();
  int? _energyLevel;
  int? _stressLevel;
  double? _sleepHours;
  bool _isLoading = false;

  final List<String> _emotions = [
    'joy',
    'sadness',
    'anger',
    'fear',
    'surprise',
    'anxiety',
    'stress',
    'excitement',
    'contentment',
    'frustration',
    'hope',
    'loneliness',
    'gratitude',
    'guilt',
    'pride',
    'love'
  ];

  final List<String> _activities = [
    'work',
    'exercise',
    'socializing',
    'family_time',
    'hobbies',
    'meditation',
    'reading',
    'music',
    'movies',
    'cooking',
    'shopping',
    'traveling',
    'studying',
    'gaming',
    'sleeping'
  ];

  final Map<String, String> _moodTypes = {
    'very_sad': 'Very Sad',
    'sad': 'Sad',
    'neutral': 'Neutral',
    'happy': 'Happy',
    'very_happy': 'Very Happy',
    'anxious': 'Anxious',
    'stressed': 'Stressed',
    'calm': 'Calm',
    'excited': 'Excited',
    'angry': 'Angry',
  };

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _saveMood() async {
    setState(() {
      _isLoading = true;
    });

    final moodProvider = Provider.of<MoodProvider>(context, listen: false);

    final moodData = {
      'moodLevel': _moodLevel,
      'moodType': _moodType,
      'emotions': _selectedEmotions,
      'activities': _selectedActivities,
      'notes': _notesController.text.trim(),
      'energyLevel': _energyLevel,
      'stressLevel': _stressLevel,
      'sleepHours': _sleepHours,
    };

    final result = await moodProvider.createMoodEntry(moodData);

    setState(() {
      _isLoading = false;
    });

    if (result['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Mood logged successfully!'),
          backgroundColor: AppColors.success,
        ),
      );
      context.go('/home');
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message'] ?? 'Failed to log mood'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Track Your Mood'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/home'),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Mood Level
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'How are you feeling? (1-10)',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: AppColors.getMoodColor(_moodLevel),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              _moodLevel.toString(),
                              style: const TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Slider(
                      value: _moodLevel.toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      activeColor: AppColors.getMoodColor(_moodLevel),
                      onChanged: (value) {
                        setState(() {
                          _moodLevel = value.round();
                        });
                      },
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: const [
                        Text('1 (Very Low)', style: TextStyle(fontSize: 12)),
                        Text('10 (Excellent)', style: TextStyle(fontSize: 12)),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Mood Type
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Mood Type',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    DropdownButtonFormField<String>(
                      value: _moodType,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      items: _moodTypes.entries.map((entry) {
                        return DropdownMenuItem(
                          value: entry.key,
                          child: Text(entry.value),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _moodType = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Emotions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Emotions (Select all that apply)',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _emotions.map((emotion) {
                        final isSelected = _selectedEmotions.contains(emotion);
                        return FilterChip(
                          label:
                              Text(emotion.replaceAll('_', ' ').toUpperCase()),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              if (selected) {
                                _selectedEmotions.add(emotion);
                              } else {
                                _selectedEmotions.remove(emotion);
                              }
                            });
                          },
                          selectedColor: AppColors.primary.withOpacity(0.2),
                          checkmarkColor: AppColors.primary,
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Activities
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Activities (What did you do today?)',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _activities.map((activity) {
                        final isSelected =
                            _selectedActivities.contains(activity);
                        return FilterChip(
                          label:
                              Text(activity.replaceAll('_', ' ').toUpperCase()),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              if (selected) {
                                _selectedActivities.add(activity);
                              } else {
                                _selectedActivities.remove(activity);
                              }
                            });
                          },
                          selectedColor: AppColors.secondary.withOpacity(0.2),
                          checkmarkColor: AppColors.secondary,
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Additional Metrics
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Additional Metrics (Optional)',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Energy Level
                    Text('Energy Level: ${_energyLevel ?? 'Not set'}'),
                    Slider(
                      value: (_energyLevel ?? 5).toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      onChanged: (value) {
                        setState(() {
                          _energyLevel = value.round();
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // Stress Level
                    Text('Stress Level: ${_stressLevel ?? 'Not set'}'),
                    Slider(
                      value: (_stressLevel ?? 5).toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      activeColor: AppColors.getStressColor(_stressLevel ?? 5),
                      onChanged: (value) {
                        setState(() {
                          _stressLevel = value.round();
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Notes
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Notes (Optional)',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    CustomTextField(
                      controller: _notesController,
                      label: '',
                      hintText: 'How are you feeling? What happened today?',
                      maxLines: 4,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Save Button
            CustomButton(
              text: 'Save Mood Entry',
              onPressed: _saveMood,
              isLoading: _isLoading,
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }
}
