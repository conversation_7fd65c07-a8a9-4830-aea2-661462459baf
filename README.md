# MindEasy - AI-Powered Mental Health Application

MindEasy is a comprehensive mental health platform that combines AI-driven insights with professional therapy services to provide personalized mental health support.

## Features

- **Mood Tracking**: Daily mood logging with visual analytics
- **AI-Powered Journaling**: Sentiment analysis and personalized recommendations
- **Therapist Finder**: Geolocation-based therapist discovery
- **Online Consultations**: Secure video therapy sessions
- **Payment Integration**: Secure in-app transactions
- **Data Privacy**: GDPR and HIPAA compliant

## Tech Stack

- **Frontend**: Flutter (Cross-platform mobile app)
- **Backend**: Node.js with Express.js
- **Database**: MongoDB
- **Authentication**: JWT tokens
- **Payment**: Stripe integration
- **AI**: Sentiment analysis and recommendation engine

## Project Structure

```
MindEasy/
├── backend/                 # Node.js/Express API server
│   ├── src/
│   │   ├── controllers/     # API controllers
│   │   ├── models/         # MongoDB schemas
│   │   ├── routes/         # API routes
│   │   ├── middleware/     # Authentication & validation
│   │   ├── services/       # Business logic
│   │   └── utils/          # Helper functions
│   ├── package.json
│   └── server.js
├── frontend/               # Flutter mobile application
│   ├── lib/
│   │   ├── screens/        # UI screens
│   │   ├── widgets/        # Reusable components
│   │   ├── services/       # API services
│   │   ├── models/         # Data models
│   │   ├── providers/      # State management
│   │   └── utils/          # Helper functions
│   ├── pubspec.yaml
│   └── main.dart
└── README.md

```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- Flutter SDK (v3.0 or higher)
- MongoDB (local or cloud instance)
- Android Studio / VS Code

### Backend Setup

1. Navigate to backend directory:
   ```bash
   cd backend
   npm install
   npm run dev
   ```

2. Set up environment variables in `.env` file

### Frontend Setup

1. Navigate to frontend directory:
   ```bash
   cd frontend
   flutter pub get
   flutter run
   ```

## API Documentation

The backend provides RESTful APIs for all application features. See `/backend/docs/` for detailed API documentation.

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

This project is licensed under the MIT License.
