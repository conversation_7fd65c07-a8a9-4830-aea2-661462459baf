class Consultation {
  final String id;
  final String userId;
  final String therapistId;
  final String therapistName;
  final String? therapistProfilePicture;
  final DateTime scheduledAt;
  final int duration; // in minutes
  final String type; // 'online', 'in-person'
  final String status; // 'scheduled', 'confirmed', 'in-progress', 'completed', 'cancelled'
  final double amount;
  final String currency;
  final String? paymentId;
  final String? paymentStatus;
  final String? meetingLink;
  final String? meetingId;
  final String? notes;
  final String? cancellationReason;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Consultation({
    required this.id,
    required this.userId,
    required this.therapistId,
    required this.therapistName,
    this.therapistProfilePicture,
    required this.scheduledAt,
    required this.duration,
    required this.type,
    required this.status,
    required this.amount,
    required this.currency,
    this.paymentId,
    this.paymentStatus,
    this.meetingLink,
    this.meetingId,
    this.notes,
    this.cancellationReason,
    required this.createdAt,
    this.updatedAt,
  });

  factory Consultation.fromJson(Map<String, dynamic> json) {
    return Consultation(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      therapistId: json['therapistId'] ?? '',
      therapistName: json['therapistName'] ?? '',
      therapistProfilePicture: json['therapistProfilePicture'],
      scheduledAt: DateTime.parse(json['scheduledAt']),
      duration: json['duration'] ?? 60,
      type: json['type'] ?? 'online',
      status: json['status'] ?? 'scheduled',
      amount: (json['amount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'USD',
      paymentId: json['paymentId'],
      paymentStatus: json['paymentStatus'],
      meetingLink: json['meetingLink'],
      meetingId: json['meetingId'],
      notes: json['notes'],
      cancellationReason: json['cancellationReason'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'therapistId': therapistId,
      'therapistName': therapistName,
      'therapistProfilePicture': therapistProfilePicture,
      'scheduledAt': scheduledAt.toIso8601String(),
      'duration': duration,
      'type': type,
      'status': status,
      'amount': amount,
      'currency': currency,
      'paymentId': paymentId,
      'paymentStatus': paymentStatus,
      'meetingLink': meetingLink,
      'meetingId': meetingId,
      'notes': notes,
      'cancellationReason': cancellationReason,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  bool get canJoin => status == 'confirmed' && 
      DateTime.now().isAfter(scheduledAt.subtract(Duration(minutes: 15))) &&
      DateTime.now().isBefore(scheduledAt.add(Duration(minutes: duration + 15)));

  bool get canCancel => status == 'scheduled' || status == 'confirmed';

  String get statusText {
    switch (status) {
      case 'scheduled': return 'Scheduled';
      case 'confirmed': return 'Confirmed';
      case 'in-progress': return 'In Progress';
      case 'completed': return 'Completed';
      case 'cancelled': return 'Cancelled';
      default: return status;
    }
  }

  String get typeText {
    switch (type) {
      case 'online': return 'Online Session';
      case 'in-person': return 'In-Person Session';
      default: return type;
    }
  }
}

class Payment {
  final String id;
  final String userId;
  final String? consultationId;
  final double amount;
  final String currency;
  final String status; // 'pending', 'completed', 'failed', 'refunded'
  final String method; // 'stripe', 'paypal', 'apple_pay', 'google_pay'
  final String? stripePaymentIntentId;
  final String? stripeChargeId;
  final String? description;
  final DateTime createdAt;
  final DateTime? completedAt;

  Payment({
    required this.id,
    required this.userId,
    this.consultationId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.method,
    this.stripePaymentIntentId,
    this.stripeChargeId,
    this.description,
    required this.createdAt,
    this.completedAt,
  });

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      consultationId: json['consultationId'],
      amount: (json['amount'] ?? 0.0).toDouble(),
      currency: json['currency'] ?? 'USD',
      status: json['status'] ?? 'pending',
      method: json['method'] ?? 'stripe',
      stripePaymentIntentId: json['stripePaymentIntentId'],
      stripeChargeId: json['stripeChargeId'],
      description: json['description'],
      createdAt: DateTime.parse(json['createdAt']),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'consultationId': consultationId,
      'amount': amount,
      'currency': currency,
      'status': status,
      'method': method,
      'stripePaymentIntentId': stripePaymentIntentId,
      'stripeChargeId': stripeChargeId,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  String get statusText {
    switch (status) {
      case 'pending': return 'Pending';
      case 'completed': return 'Completed';
      case 'failed': return 'Failed';
      case 'refunded': return 'Refunded';
      default: return status;
    }
  }

  String get methodText {
    switch (method) {
      case 'stripe': return 'Credit Card';
      case 'paypal': return 'PayPal';
      case 'apple_pay': return 'Apple Pay';
      case 'google_pay': return 'Google Pay';
      default: return method;
    }
  }

  String get formattedAmount => '\$${amount.toStringAsFixed(2)}';
}
