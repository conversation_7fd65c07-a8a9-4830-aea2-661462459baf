class Location {
  final double latitude;
  final double longitude;
  final String? address;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;

  Location({
    required this.latitude,
    required this.longitude,
    this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      address: json['address'],
      city: json['city'],
      state: json['state'],
      country: json['country'],
      postalCode: json['postalCode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postalCode': postalCode,
    };
  }

  String get fullAddress {
    List<String> parts = [];
    if (address != null) parts.add(address!);
    if (city != null) parts.add(city!);
    if (state != null) parts.add(state!);
    if (postalCode != null) parts.add(postalCode!);
    return parts.join(', ');
  }

  // Calculate distance between two locations in kilometers
  double distanceTo(Location other) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    double lat1Rad = latitude * (3.14159265359 / 180);
    double lat2Rad = other.latitude * (3.14159265359 / 180);
    double deltaLatRad = (other.latitude - latitude) * (3.14159265359 / 180);
    double deltaLngRad = (other.longitude - longitude) * (3.14159265359 / 180);

    double a = (deltaLatRad / 2).sin() * (deltaLatRad / 2).sin() +
        lat1Rad.cos() * lat2Rad.cos() *
        (deltaLngRad / 2).sin() * (deltaLngRad / 2).sin();
    double c = 2 * (a.sqrt()).asin();

    return earthRadius * c;
  }
}

class TherapistLocation {
  final String therapistId;
  final Location location;
  final String clinicName;
  final String? clinicAddress;
  final String? phone;
  final List<String> availableDays;
  final Map<String, String> workingHours; // day -> "09:00-17:00"
  final bool isOnlineAvailable;
  final bool isInPersonAvailable;

  TherapistLocation({
    required this.therapistId,
    required this.location,
    required this.clinicName,
    this.clinicAddress,
    this.phone,
    required this.availableDays,
    required this.workingHours,
    required this.isOnlineAvailable,
    required this.isInPersonAvailable,
  });

  factory TherapistLocation.fromJson(Map<String, dynamic> json) {
    return TherapistLocation(
      therapistId: json['therapistId'] ?? '',
      location: Location.fromJson(json['location'] ?? {}),
      clinicName: json['clinicName'] ?? '',
      clinicAddress: json['clinicAddress'],
      phone: json['phone'],
      availableDays: List<String>.from(json['availableDays'] ?? []),
      workingHours: Map<String, String>.from(json['workingHours'] ?? {}),
      isOnlineAvailable: json['isOnlineAvailable'] ?? false,
      isInPersonAvailable: json['isInPersonAvailable'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'therapistId': therapistId,
      'location': location.toJson(),
      'clinicName': clinicName,
      'clinicAddress': clinicAddress,
      'phone': phone,
      'availableDays': availableDays,
      'workingHours': workingHours,
      'isOnlineAvailable': isOnlineAvailable,
      'isInPersonAvailable': isInPersonAvailable,
    };
  }
}

class NearbyTherapist {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String? profilePicture;
  final List<String> specializations;
  final List<String> languages;
  final double rating;
  final int reviewCount;
  final double hourlyRate;
  final TherapistLocation location;
  final double distance; // in kilometers
  final bool isVerified;
  final bool isAvailable;

  NearbyTherapist({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.profilePicture,
    required this.specializations,
    required this.languages,
    required this.rating,
    required this.reviewCount,
    required this.hourlyRate,
    required this.location,
    required this.distance,
    required this.isVerified,
    required this.isAvailable,
  });

  factory NearbyTherapist.fromJson(Map<String, dynamic> json) {
    return NearbyTherapist(
      id: json['_id'] ?? json['id'] ?? '',
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      email: json['email'] ?? '',
      profilePicture: json['profilePicture'],
      specializations: List<String>.from(json['specializations'] ?? []),
      languages: List<String>.from(json['languages'] ?? []),
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      hourlyRate: (json['hourlyRate'] ?? 0.0).toDouble(),
      location: TherapistLocation.fromJson(json['location'] ?? {}),
      distance: (json['distance'] ?? 0.0).toDouble(),
      isVerified: json['isVerified'] ?? false,
      isAvailable: json['isAvailable'] ?? false,
    );
  }

  String get fullName => '$firstName $lastName';
  
  String get specializationsText => specializations.join(', ');
  
  String get languagesText => languages.join(', ');
  
  String get distanceText {
    if (distance < 1) {
      return '${(distance * 1000).round()}m away';
    } else {
      return '${distance.toStringAsFixed(1)}km away';
    }
  }
}
