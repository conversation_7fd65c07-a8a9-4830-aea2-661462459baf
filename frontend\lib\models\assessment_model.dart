class Assessment {
  final String id;
  final String userId;
  final String type; // 'anxiety', 'depression', 'stress', 'phq9', 'gad7'
  final String title;
  final List<AssessmentQuestion> questions;
  final int? score;
  final String? severity;
  final String? interpretation;
  final DateTime createdAt;
  final DateTime? completedAt;
  final bool isCompleted;

  Assessment({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.questions,
    this.score,
    this.severity,
    this.interpretation,
    required this.createdAt,
    this.completedAt,
    required this.isCompleted,
  });

  factory Assessment.fromJson(Map<String, dynamic> json) {
    return Assessment(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      type: json['type'] ?? '',
      title: json['title'] ?? '',
      questions: (json['questions'] as List<dynamic>?)
          ?.map((q) => AssessmentQuestion.fromJson(q))
          .toList() ?? [],
      score: json['score'],
      severity: json['severity'],
      interpretation: json['interpretation'],
      createdAt: DateTime.parse(json['createdAt']),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
      isCompleted: json['isCompleted'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type,
      'title': title,
      'questions': questions.map((q) => q.toJson()).toList(),
      'score': score,
      'severity': severity,
      'interpretation': interpretation,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'isCompleted': isCompleted,
    };
  }
}

class AssessmentQuestion {
  final String id;
  final String question;
  final List<AssessmentOption> options;
  final int? selectedValue;
  final String? selectedText;

  AssessmentQuestion({
    required this.id,
    required this.question,
    required this.options,
    this.selectedValue,
    this.selectedText,
  });

  factory AssessmentQuestion.fromJson(Map<String, dynamic> json) {
    return AssessmentQuestion(
      id: json['id'] ?? '',
      question: json['question'] ?? '',
      options: (json['options'] as List<dynamic>?)
          ?.map((o) => AssessmentOption.fromJson(o))
          .toList() ?? [],
      selectedValue: json['selectedValue'],
      selectedText: json['selectedText'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options.map((o) => o.toJson()).toList(),
      'selectedValue': selectedValue,
      'selectedText': selectedText,
    };
  }

  AssessmentQuestion copyWith({
    String? id,
    String? question,
    List<AssessmentOption>? options,
    int? selectedValue,
    String? selectedText,
  }) {
    return AssessmentQuestion(
      id: id ?? this.id,
      question: question ?? this.question,
      options: options ?? this.options,
      selectedValue: selectedValue ?? this.selectedValue,
      selectedText: selectedText ?? this.selectedText,
    );
  }
}

class AssessmentOption {
  final String text;
  final int value;

  AssessmentOption({
    required this.text,
    required this.value,
  });

  factory AssessmentOption.fromJson(Map<String, dynamic> json) {
    return AssessmentOption(
      text: json['text'] ?? '',
      value: json['value'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'value': value,
    };
  }
}

class AssessmentResult {
  final int totalScore;
  final String severity;
  final String interpretation;
  final List<String> recommendations;
  final bool needsProfessionalHelp;

  AssessmentResult({
    required this.totalScore,
    required this.severity,
    required this.interpretation,
    required this.recommendations,
    required this.needsProfessionalHelp,
  });

  factory AssessmentResult.fromJson(Map<String, dynamic> json) {
    return AssessmentResult(
      totalScore: json['totalScore'] ?? 0,
      severity: json['severity'] ?? '',
      interpretation: json['interpretation'] ?? '',
      recommendations: List<String>.from(json['recommendations'] ?? []),
      needsProfessionalHelp: json['needsProfessionalHelp'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalScore': totalScore,
      'severity': severity,
      'interpretation': interpretation,
      'recommendations': recommendations,
      'needsProfessionalHelp': needsProfessionalHelp,
    };
  }
}
