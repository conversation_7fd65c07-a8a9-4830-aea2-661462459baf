const mongoose = require('mongoose');

const journalSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  title: {
    type: String,
    required: [true, 'Journal title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  content: {
    type: String,
    required: [true, 'Journal content is required'],
    maxlength: [5000, 'Content cannot exceed 5000 characters']
  },
  mood: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Mood'
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  category: {
    type: String,
    enum: [
      'daily_reflection', 'gratitude', 'goals', 'challenges',
      'relationships', 'work', 'health', 'personal_growth',
      'therapy_notes', 'dreams', 'achievements', 'struggles'
    ],
    default: 'daily_reflection'
  },
  isPrivate: {
    type: Boolean,
    default: true
  },
  attachments: [{
    type: {
      type: String,
      enum: ['image', 'audio', 'video'],
      required: true
    },
    url: {
      type: String,
      required: true
    },
    filename: String,
    size: Number,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  aiAnalysis: {
    sentimentScore: {
      type: Number,
      min: [-1, 'Sentiment score must be between -1 and 1'],
      max: [1, 'Sentiment score must be between -1 and 1']
    },
    emotions: [{
      emotion: String,
      confidence: {
        type: Number,
        min: [0, 'Confidence must be between 0 and 1'],
        max: [1, 'Confidence must be between 0 and 1']
      }
    }],
    keyThemes: [String],
    insights: [String],
    recommendations: [String],
    riskIndicators: [{
      type: String,
      severity: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'low'
      }
    }],
    wordCount: Number,
    readingTime: Number, // in minutes
    analyzedAt: Date
  },
  readingTime: {
    type: Number, // in minutes
    default: 0
  },
  wordCount: {
    type: Number,
    default: 0
  },
  isFavorite: {
    type: Boolean,
    default: false
  },
  shareSettings: {
    isShared: {
      type: Boolean,
      default: false
    },
    sharedWith: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      permission: {
        type: String,
        enum: ['read', 'comment'],
        default: 'read'
      },
      sharedAt: {
        type: Date,
        default: Date.now
      }
    }],
    therapistAccess: {
      type: Boolean,
      default: false
    }
  },
  comments: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: {
      type: String,
      required: true,
      maxlength: [500, 'Comment cannot exceed 500 characters']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  reminderSettings: {
    hasReminder: {
      type: Boolean,
      default: false
    },
    reminderDate: Date,
    reminderMessage: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient queries
journalSchema.index({ user: 1, createdAt: -1 });
journalSchema.index({ user: 1, category: 1 });
journalSchema.index({ user: 1, tags: 1 });
journalSchema.index({ createdAt: -1 });

// Text index for search functionality
journalSchema.index({
  title: 'text',
  content: 'text',
  tags: 'text'
});

// Virtual for excerpt
journalSchema.virtual('excerpt').get(function() {
  if (!this.content) return '';
  return this.content.length > 150 
    ? this.content.substring(0, 150) + '...'
    : this.content;
});

// Virtual for formatted date
journalSchema.virtual('formattedDate').get(function() {
  return this.createdAt.toLocaleDateString();
});

// Pre-save middleware to calculate word count and reading time
journalSchema.pre('save', function(next) {
  if (this.isModified('content')) {
    // Calculate word count
    this.wordCount = this.content.trim().split(/\s+/).length;
    
    // Calculate reading time (average 200 words per minute)
    this.readingTime = Math.ceil(this.wordCount / 200);
    
    // Update AI analysis word count if it exists
    if (this.aiAnalysis) {
      this.aiAnalysis.wordCount = this.wordCount;
      this.aiAnalysis.readingTime = this.readingTime;
    }
  }
  next();
});

// Static method to get journal statistics
journalSchema.statics.getJournalStats = async function(userId, startDate, endDate) {
  const pipeline = [
    {
      $match: {
        user: mongoose.Types.ObjectId(userId),
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: null,
        totalEntries: { $sum: 1 },
        totalWords: { $sum: '$wordCount' },
        averageWordsPerEntry: { $avg: '$wordCount' },
        totalReadingTime: { $sum: '$readingTime' },
        categoriesUsed: { $addToSet: '$category' },
        averageSentiment: { $avg: '$aiAnalysis.sentimentScore' }
      }
    }
  ];
  
  return await this.aggregate(pipeline);
};

// Static method to search journals
journalSchema.statics.searchJournals = async function(userId, query, options = {}) {
  const {
    category,
    tags,
    startDate,
    endDate,
    limit = 20,
    skip = 0
  } = options;
  
  const searchCriteria = {
    user: userId,
    $text: { $search: query }
  };
  
  if (category) {
    searchCriteria.category = category;
  }
  
  if (tags && tags.length > 0) {
    searchCriteria.tags = { $in: tags };
  }
  
  if (startDate || endDate) {
    searchCriteria.createdAt = {};
    if (startDate) searchCriteria.createdAt.$gte = new Date(startDate);
    if (endDate) searchCriteria.createdAt.$lte = new Date(endDate);
  }
  
  return await this.find(searchCriteria)
    .sort({ score: { $meta: 'textScore' }, createdAt: -1 })
    .limit(limit)
    .skip(skip)
    .populate('mood', 'moodLevel moodType date');
};

module.exports = mongoose.model('Journal', journalSchema);
