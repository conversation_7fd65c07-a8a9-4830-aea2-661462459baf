import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../providers/journal_provider.dart';
import '../../utils/app_colors.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';

class JournalEntryScreen extends StatefulWidget {
  final String? journalId;

  const JournalEntryScreen({super.key, this.journalId});

  @override
  State<JournalEntryScreen> createState() => _JournalEntryScreenState();
}

class _JournalEntryScreenState extends State<JournalEntryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  String _selectedCategory = 'daily_reflection';
  final List<String> _selectedTags = [];
  bool _isPrivate = true;
  bool _isLoading = false;

  final List<String> _categories = [
    'daily_reflection',
    'gratitude',
    'goals',
    'challenges',
    'relationships',
    'work',
    'health',
    'personal_growth',
    'therapy_notes',
    'dreams',
    'achievements',
    'struggles'
  ];

  final Map<String, String> _categoryLabels = {
    'daily_reflection': 'Daily Reflection',
    'gratitude': 'Gratitude',
    'goals': 'Goals',
    'challenges': 'Challenges',
    'relationships': 'Relationships',
    'work': 'Work',
    'health': 'Health',
    'personal_growth': 'Personal Growth',
    'therapy_notes': 'Therapy Notes',
    'dreams': 'Dreams',
    'achievements': 'Achievements',
    'struggles': 'Struggles',
  };

  final List<String> _availableTags = [
    'mood',
    'anxiety',
    'stress',
    'happiness',
    'sadness',
    'anger',
    'fear',
    'love',
    'hope',
    'gratitude',
    'mindfulness',
    'meditation',
    'exercise',
    'sleep',
    'work',
    'family',
    'friends',
    'therapy',
    'progress',
    'setback',
    'breakthrough',
    'reflection'
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  Future<void> _saveJournal() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    final journalProvider =
        Provider.of<JournalProvider>(context, listen: false);

    final journalData = {
      'title': _titleController.text.trim(),
      'content': _contentController.text.trim(),
      'category': _selectedCategory,
      'tags': _selectedTags,
      'isPrivate': _isPrivate,
    };

    final result = await journalProvider.createJournal(journalData);

    setState(() {
      _isLoading = false;
    });

    if (result['success']) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Journal entry saved successfully!'),
          backgroundColor: AppColors.success,
        ),
      );
      context.go('/journals');
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message'] ?? 'Failed to save journal entry'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.journalId != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Journal Entry' : 'New Journal Entry'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/journals'),
        ),
        actions: [
          IconButton(
            icon: Icon(_isPrivate ? Icons.lock : Icons.public),
            onPressed: () {
              setState(() {
                _isPrivate = !_isPrivate;
              });
            },
            tooltip: _isPrivate ? 'Private Entry' : 'Public Entry',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              CustomTextField(
                controller: _titleController,
                label: 'Title',
                hintText: 'Give your entry a title...',
                prefixIcon: Icons.title,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  if (value.trim().length < 3) {
                    return 'Title must be at least 3 characters';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Category
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Category',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide:
                            const BorderSide(color: AppColors.lightGray),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 16),
                      prefixIcon: const Icon(Icons.category,
                          color: AppColors.textSecondary, size: 20),
                    ),
                    items: _categories.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(_categoryLabels[category] ?? category),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value!;
                      });
                    },
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Content
              CustomTextField(
                controller: _contentController,
                label: 'Content',
                hintText: 'Write your thoughts, feelings, and experiences...',
                maxLines: 12,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please write some content';
                  }
                  if (value.trim().length < 10) {
                    return 'Content must be at least 10 characters';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Tags
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Tags (Optional)',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Select tags that relate to your entry:',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _availableTags.map((tag) {
                      final isSelected = _selectedTags.contains(tag);
                      return FilterChip(
                        label: Text('#$tag'),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            if (selected) {
                              _selectedTags.add(tag);
                            } else {
                              _selectedTags.remove(tag);
                            }
                          });
                        },
                        selectedColor: AppColors.primary.withOpacity(0.2),
                        checkmarkColor: AppColors.primary,
                      );
                    }).toList(),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Privacy Setting
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        _isPrivate ? Icons.lock : Icons.public,
                        color:
                            _isPrivate ? AppColors.warning : AppColors.success,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _isPrivate ? 'Private Entry' : 'Public Entry',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            Text(
                              _isPrivate
                                  ? 'Only you can see this entry'
                                  : 'This entry may be visible to others',
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Switch(
                        value: _isPrivate,
                        onChanged: (value) {
                          setState(() {
                            _isPrivate = value;
                          });
                        },
                        activeColor: AppColors.primary,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Save Button
              CustomButton(
                text: isEditing ? 'Update Entry' : 'Save Entry',
                onPressed: _saveJournal,
                isLoading: _isLoading,
                width: double.infinity,
                icon: isEditing ? Icons.update : Icons.save,
              ),

              const SizedBox(height: 16),

              // Cancel Button
              CustomButton(
                text: 'Cancel',
                onPressed: () => context.go('/journals'),
                variant: ButtonVariant.outline,
                fullWidth: true,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
