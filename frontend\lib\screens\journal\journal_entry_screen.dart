import 'package:flutter/material.dart';
import '../../utils/app_colors.dart';

class JournalEntryScreen extends StatelessWidget {
  final String? journalId;
  
  const JournalEntryScreen({super.key, this.journalId});

  @override
  Widget build(BuildContext context) {
    final isEditing = journalId != null;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Journal Entry' : 'New Journal Entry'),
      ),
      body: const Center(
        child: Text(
          'Journal entry form will be implemented here',
          style: TextStyle(
            fontSize: 16,
            color: AppColors.textSecondary,
          ),
        ),
      ),
    );
  }
}
