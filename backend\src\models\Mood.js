const mongoose = require('mongoose');

const moodSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User is required']
  },
  date: {
    type: Date,
    required: [true, 'Date is required'],
    default: Date.now
  },
  moodLevel: {
    type: Number,
    required: [true, 'Mood level is required'],
    min: [1, 'Mood level must be between 1 and 10'],
    max: [10, 'Mood level must be between 1 and 10']
  },
  moodType: {
    type: String,
    enum: [
      'very_sad', 'sad', 'neutral', 'happy', 'very_happy',
      'anxious', 'stressed', 'calm', 'excited', 'angry',
      'frustrated', 'content', 'overwhelmed', 'peaceful', 'energetic'
    ],
    required: [true, 'Mood type is required']
  },
  emotions: [{
    type: String,
    enum: [
      'joy', 'sadness', 'anger', 'fear', 'surprise', 'disgust',
      'anxiety', 'stress', 'excitement', 'contentment', 'frustration',
      'hope', 'loneliness', 'gratitude', 'guilt', 'shame', 'pride',
      'love', 'jealousy', 'envy', 'compassion', 'empathy'
    ]
  }],
  triggers: [{
    type: String,
    maxlength: [100, 'Trigger description cannot exceed 100 characters']
  }],
  activities: [{
    type: String,
    enum: [
      'work', 'exercise', 'socializing', 'family_time', 'hobbies',
      'meditation', 'reading', 'music', 'movies', 'cooking',
      'shopping', 'traveling', 'studying', 'gaming', 'sleeping',
      'eating', 'therapy', 'medication', 'outdoor_activities'
    ]
  }],
  sleepHours: {
    type: Number,
    min: [0, 'Sleep hours cannot be negative'],
    max: [24, 'Sleep hours cannot exceed 24']
  },
  energyLevel: {
    type: Number,
    min: [1, 'Energy level must be between 1 and 10'],
    max: [10, 'Energy level must be between 1 and 10']
  },
  stressLevel: {
    type: Number,
    min: [1, 'Stress level must be between 1 and 10'],
    max: [10, 'Stress level must be between 1 and 10']
  },
  socialInteraction: {
    type: String,
    enum: ['none', 'minimal', 'moderate', 'high'],
    default: 'moderate'
  },
  weather: {
    type: String,
    enum: ['sunny', 'cloudy', 'rainy', 'snowy', 'stormy', 'foggy']
  },
  notes: {
    type: String,
    maxlength: [500, 'Notes cannot exceed 500 characters']
  },
  location: {
    type: String,
    maxlength: [100, 'Location cannot exceed 100 characters']
  },
  isPrivate: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  aiAnalysis: {
    sentimentScore: {
      type: Number,
      min: [-1, 'Sentiment score must be between -1 and 1'],
      max: [1, 'Sentiment score must be between -1 and 1']
    },
    emotionalPatterns: [String],
    recommendations: [String],
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'low'
    },
    analyzedAt: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound index for user and date (for efficient queries)
moodSchema.index({ user: 1, date: -1 });

// Index for date range queries
moodSchema.index({ date: -1 });

// Virtual for mood description
moodSchema.virtual('moodDescription').get(function() {
  const descriptions = {
    1: 'Extremely Low',
    2: 'Very Low',
    3: 'Low',
    4: 'Below Average',
    5: 'Average',
    6: 'Above Average',
    7: 'Good',
    8: 'Very Good',
    9: 'Excellent',
    10: 'Outstanding'
  };
  return descriptions[this.moodLevel] || 'Unknown';
});

// Virtual for formatted date
moodSchema.virtual('formattedDate').get(function() {
  return this.date.toLocaleDateString();
});

// Static method to get mood statistics for a user
moodSchema.statics.getMoodStats = async function(userId, startDate, endDate) {
  const pipeline = [
    {
      $match: {
        user: mongoose.Types.ObjectId(userId),
        date: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: null,
        averageMood: { $avg: '$moodLevel' },
        averageEnergy: { $avg: '$energyLevel' },
        averageStress: { $avg: '$stressLevel' },
        totalEntries: { $sum: 1 },
        moodDistribution: {
          $push: {
            level: '$moodLevel',
            type: '$moodType',
            date: '$date'
          }
        }
      }
    }
  ];
  
  return await this.aggregate(pipeline);
};

// Static method to get mood trends
moodSchema.statics.getMoodTrends = async function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return await this.find({
    user: userId,
    date: { $gte: startDate }
  })
  .sort({ date: 1 })
  .select('date moodLevel energyLevel stressLevel moodType');
};

// Pre-save middleware to set analyzed date for AI analysis
moodSchema.pre('save', function(next) {
  if (this.isModified('aiAnalysis') && this.aiAnalysis) {
    this.aiAnalysis.analyzedAt = new Date();
  }
  next();
});

module.exports = mongoose.model('Mood', moodSchema);
