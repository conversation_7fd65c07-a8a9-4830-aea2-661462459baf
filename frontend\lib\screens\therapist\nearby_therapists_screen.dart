import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../providers/location_provider.dart';
import '../../models/location_model.dart';
import '../../utils/app_colors.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_card.dart';

class NearbyTherapistsScreen extends StatefulWidget {
  const NearbyTherapistsScreen({super.key});

  @override
  State<NearbyTherapistsScreen> createState() => _NearbyTherapistsScreenState();
}

class _NearbyTherapistsScreenState extends State<NearbyTherapistsScreen> {
  GoogleMapController? _mapController;
  bool _showMap = true;
  String _selectedFilter = 'distance';
  double _radiusKm = 25.0;
  List<String> _selectedSpecializations = [];
  double? _maxHourlyRate;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeLocation();
    });
  }

  Future<void> _initializeLocation() async {
    final locationProvider = context.read<LocationProvider>();
    await locationProvider.initializeLocation();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'Nearby Therapists',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        backgroundColor: AppColors.surface,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textPrimary),
        actions: [
          IconButton(
            icon: Icon(
              _showMap ? Icons.list : Icons.map,
              color: AppColors.textPrimary,
            ),
            onPressed: () {
              setState(() {
                _showMap = !_showMap;
              });
            },
          ),
          IconButton(
            icon: const Icon(
              Icons.filter_list,
              color: AppColors.textPrimary,
            ),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Consumer<LocationProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            );
          }

          if (provider.error != null) {
            return _buildErrorState(provider.error!);
          }

          if (!provider.isLocationEnabled) {
            return _buildLocationDisabledState();
          }

          if (provider.currentLocation == null) {
            return _buildNoLocationState();
          }

          return Column(
            children: [
              // Location Info Bar
              _buildLocationInfoBar(provider),
              
              // Content
              Expanded(
                child: _showMap 
                  ? _buildMapView(provider)
                  : _buildListView(provider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLocationInfoBar(LocationProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: Row(
        children: [
          const Icon(
            Icons.location_on,
            color: AppColors.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              provider.currentLocation?.fullAddress ?? 'Current Location',
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Text(
            '${provider.nearbyTherapists.length} therapists found',
            style: const TextStyle(
              color: AppColors.primary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapView(LocationProvider provider) {
    if (provider.currentLocation == null) return const SizedBox();

    final markers = <Marker>{};
    
    // Add user location marker
    markers.add(
      Marker(
        markerId: const MarkerId('user_location'),
        position: LatLng(
          provider.currentLocation!.latitude,
          provider.currentLocation!.longitude,
        ),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        infoWindow: const InfoWindow(title: 'Your Location'),
      ),
    );

    // Add therapist markers
    for (int i = 0; i < provider.nearbyTherapists.length; i++) {
      final therapist = provider.nearbyTherapists[i];
      markers.add(
        Marker(
          markerId: MarkerId('therapist_$i'),
          position: LatLng(
            therapist.location.location.latitude,
            therapist.location.location.longitude,
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
          infoWindow: InfoWindow(
            title: therapist.fullName,
            snippet: '${therapist.distanceText} • \$${therapist.hourlyRate}/hr',
          ),
          onTap: () => _showTherapistBottomSheet(therapist),
        ),
      );
    }

    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: LatLng(
          provider.currentLocation!.latitude,
          provider.currentLocation!.longitude,
        ),
        zoom: 12,
      ),
      markers: markers,
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
      },
      myLocationEnabled: true,
      myLocationButtonEnabled: true,
    );
  }

  Widget _buildListView(LocationProvider provider) {
    if (provider.nearbyTherapists.isEmpty) {
      return _buildNoTherapistsState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: provider.nearbyTherapists.length,
      itemBuilder: (context, index) {
        final therapist = provider.nearbyTherapists[index];
        return _buildTherapistCard(therapist);
      },
    );
  }

  Widget _buildTherapistCard(NearbyTherapist therapist) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: CustomCard(
        variant: CardVariant.elevated,
        onTap: () => _showTherapistDetails(therapist),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: AppColors.primary.withOpacity(0.1),
                    backgroundImage: therapist.profilePicture != null
                        ? NetworkImage(therapist.profilePicture!)
                        : null,
                    child: therapist.profilePicture == null
                        ? const Icon(
                            Icons.person,
                            color: AppColors.primary,
                            size: 30,
                          )
                        : null,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                therapist.fullName,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ),
                            if (therapist.isVerified)
                              const Icon(
                                Icons.verified,
                                color: AppColors.success,
                                size: 20,
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${therapist.rating.toStringAsFixed(1)} (${therapist.reviewCount} reviews)',
                              style: const TextStyle(
                                color: AppColors.textSecondary,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          therapist.distanceText,
                          style: const TextStyle(
                            color: AppColors.textTertiary,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                therapist.specializationsText,
                style: const TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.success.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '\$${therapist.hourlyRate.toStringAsFixed(0)}/hr',
                      style: const TextStyle(
                        color: AppColors.success,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (therapist.location.isOnlineAvailable)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.info.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Online',
                        style: TextStyle(
                          color: AppColors.info,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  const SizedBox(width: 8),
                  if (therapist.location.isInPersonAvailable)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.secondary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'In-Person',
                        style: TextStyle(
                          color: AppColors.secondary,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  const Spacer(),
                  CustomButton(
                    text: 'Book',
                    variant: ButtonVariant.primary,
                    size: ButtonSize.small,
                    onPressed: () => _bookConsultation(therapist),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'Retry',
              variant: ButtonVariant.primary,
              onPressed: _initializeLocation,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationDisabledState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.location_disabled,
              size: 64,
              color: AppColors.warning,
            ),
            const SizedBox(height: 16),
            Text(
              'Location Required',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Please enable location services to find nearby therapists.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'Enable Location',
              variant: ButtonVariant.primary,
              onPressed: _initializeLocation,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoLocationState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
      ),
    );
  }

  Widget _buildNoTherapistsState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 64,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: 16),
            Text(
              'No Therapists Found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try expanding your search radius or adjusting your filters.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            CustomButton(
              text: 'Adjust Filters',
              variant: ButtonVariant.outline,
              onPressed: _showFilterDialog,
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterDialog() {
    // TODO: Implement filter dialog
  }

  void _showTherapistBottomSheet(NearbyTherapist therapist) {
    // TODO: Implement therapist bottom sheet
  }

  void _showTherapistDetails(NearbyTherapist therapist) {
    // TODO: Navigate to therapist details screen
  }

  void _bookConsultation(NearbyTherapist therapist) {
    // TODO: Navigate to booking screen
  }
}
