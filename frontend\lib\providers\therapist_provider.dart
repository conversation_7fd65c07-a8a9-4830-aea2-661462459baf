import 'package:flutter/foundation.dart';
import '../services/api_service.dart';

class TherapistProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  List<Map<String, dynamic>> _therapists = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Map<String, dynamic>> get therapists => _therapists;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get therapists
  Future<void> getTherapists() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final response = await _apiService.get('/therapists');
      
      if (response['success']) {
        _therapists = List<Map<String, dynamic>>.from(response['data'] ?? []);
      }
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
