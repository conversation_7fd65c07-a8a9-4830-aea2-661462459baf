const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Therapist = require('../models/Therapist');

// Generate JWT token
const generateToken = (id, role = 'user') => {
  return jwt.sign(
    { id, role },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE || '7d' }
  );
};

// Protect routes - general authentication
const protect = async (req, res, next) => {
  try {
    let token;

    console.log('Auth middleware - Headers:', req.headers.authorization);

    // Check for token in headers
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
      console.log('Auth middleware - Token extracted:', token?.substring(0, 20) + '...');
    }

    if (!token) {
      console.log('Auth middleware - No token found');
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log('Auth middleware - Token decoded:', { id: decoded.id, role: decoded.role });

      // Get user based on role
      let user;
      if (decoded.role === 'therapist') {
        user = await Therapist.findById(decoded.id).select('-password');
      } else {
        user = await User.findById(decoded.id).select('-password');
      }

      console.log('Auth middleware - User found:', user ? { id: user._id, email: user.email, isActive: user.isActive } : 'null');

      if (!user) {
        console.log('Auth middleware - No user found with token');
        return res.status(401).json({
          success: false,
          message: 'No user found with this token'
        });
      }

      // Check if user is active
      if (!user.isActive) {
        console.log('Auth middleware - User account deactivated');
        return res.status(401).json({
          success: false,
          message: 'User account is deactivated'
        });
      }

      console.log('Auth middleware - Authentication successful');
      req.user = user;
      req.userRole = decoded.role;
      next();
    } catch (error) {
      console.log('Auth middleware - Token verification error:', error.message);
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Server error in authentication'
    });
  }
};

// Authorize specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.userRole)) {
      return res.status(403).json({
        success: false,
        message: `User role ${req.userRole} is not authorized to access this route`
      });
    }
    next();
  };
};

// Protect user routes (only users can access)
const protectUser = async (req, res, next) => {
  try {
    let token;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.role !== 'user') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Users only.'
      });
    }

    const user = await User.findById(decoded.id).select('-password');

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'User not found or inactive'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Not authorized to access this route'
    });
  }
};

// Protect therapist routes (only therapists can access)
const protectTherapist = async (req, res, next) => {
  try {
    let token;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.role !== 'therapist') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Therapists only.'
      });
    }

    const therapist = await Therapist.findById(decoded.id).select('-password');

    if (!therapist || !therapist.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Therapist not found or inactive'
      });
    }

    if (!therapist.isVerified) {
      return res.status(403).json({
        success: false,
        message: 'Therapist account not verified'
      });
    }

    req.user = therapist;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Not authorized to access this route'
    });
  }
};

// Check if user owns the resource
const checkOwnership = (Model, paramName = 'id') => {
  return async (req, res, next) => {
    try {
      const resource = await Model.findById(req.params[paramName]);
      
      if (!resource) {
        return res.status(404).json({
          success: false,
          message: 'Resource not found'
        });
      }

      // Check if user owns the resource
      if (resource.user && resource.user.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to access this resource'
        });
      }

      req.resource = resource;
      next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Server error checking ownership'
      });
    }
  };
};

// Rate limiting for sensitive operations
const sensitiveOperationLimit = require('express-rate-limit')({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    success: false,
    message: 'Too many attempts, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Optional authentication (for public routes that can benefit from user context)
const optionalAuth = async (req, res, next) => {
  try {
    let token;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        let user;
        if (decoded.role === 'therapist') {
          user = await Therapist.findById(decoded.id).select('-password');
        } else {
          user = await User.findById(decoded.id).select('-password');
        }

        if (user && user.isActive) {
          req.user = user;
          req.userRole = decoded.role;
        }
      } catch (error) {
        // Token invalid, but continue without user context
      }
    }

    next();
  } catch (error) {
    next();
  }
};

module.exports = {
  generateToken,
  protect,
  authorize,
  protectUser,
  protectTherapist,
  checkOwnership,
  sensitiveOperationLimit,
  optionalAuth
};
