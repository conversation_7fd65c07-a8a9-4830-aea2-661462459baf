import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Modern Purple/Violet
  static const Color primary = Color(0xFF7C3AED); // Violet-600
  static const Color primaryLight = Color(0xFF8B5CF6); // Violet-500
  static const Color primaryDark = Color(0xFF6D28D9); // Violet-700
  static const Color primarySurface = Color(0xFFF5F3FF); // Violet-50
  static const Color primaryContainer = Color(0xFFEDE9FE); // Violet-100

  // Secondary Colors - Teal/Cyan
  static const Color secondary = Color(0xFF0891B2); // Cyan-600
  static const Color secondaryLight = Color(0xFF06B6D4); // Cyan-500
  static const Color secondaryDark = Color(0xFF0E7490); // Cyan-700
  static const Color secondarySurface = Color(0xFFECFEFF); // Cyan-50
  static const Color secondaryContainer = Color(0xFFCFFAFE); // Cyan-100

  // Accent Colors - Rose/Pink
  static const Color accent = Color(0xFFE11D48); // Rose-600
  static const Color accentLight = Color(0xFFF43F5E); // Rose-500
  static const Color accentDark = Color(0xFFBE185D); // Rose-700
  static const Color accentSurface = Color(0xFFFFF1F2); // Rose-50
  static const Color accentContainer = Color(0xFFFFE4E6); // Rose-100

  // Neutral Colors - Modern grays with better contrast
  static const Color background = Color(0xFFFAFAFA); // Neutral-50
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF4F4F5); // Neutral-100
  static const Color surfaceContainer = Color(0xFFE4E4E7); // Neutral-200
  static const Color surfaceContainerHigh = Color(0xFFD4D4D8); // Neutral-300
  static const Color outline = Color(0xFFD4D4D8); // Neutral-300
  static const Color outlineVariant = Color(0xFFA1A1AA); // Neutral-400

  // Text Colors - Better hierarchy
  static const Color textPrimary = Color(0xFF09090B); // Neutral-950
  static const Color textSecondary = Color(0xFF3F3F46); // Neutral-700
  static const Color textTertiary = Color(0xFF71717A); // Neutral-500
  static const Color textDisabled = Color(0xFFA1A1AA); // Neutral-400
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSurface = Color(0xFF09090B);

  // Status Colors - More sophisticated
  static const Color success = Color(0xFF059669); // Emerald-600
  static const Color successLight = Color(0xFF10B981); // Emerald-500
  static const Color successSurface = Color(0xFFECFDF5); // Emerald-50
  static const Color successContainer = Color(0xFFD1FAE5); // Emerald-100

  static const Color warning = Color(0xFFD97706); // Amber-600
  static const Color warningLight = Color(0xFFF59E0B); // Amber-500
  static const Color warningSurface = Color(0xFFFEFBEB); // Amber-50
  static const Color warningContainer = Color(0xFFFEF3C7); // Amber-100

  static const Color error = Color(0xFFDC2626); // Red-600
  static const Color errorLight = Color(0xFFEF4444); // Red-500
  static const Color errorSurface = Color(0xFFFEF2F2); // Red-50
  static const Color errorContainer = Color(0xFFFECDD3); // Red-100

  static const Color info = Color(0xFF2563EB); // Blue-600
  static const Color infoLight = Color(0xFF3B82F6); // Blue-500
  static const Color infoSurface = Color(0xFFEFF6FF); // Blue-50
  static const Color infoContainer = Color(0xFFDBEAFE); // Blue-100

  // Mood Colors - More sophisticated palette
  static const Color moodExcellent = Color(0xFF059669); // Emerald-600
  static const Color moodGood = Color(0xFF10B981); // Emerald-500
  static const Color moodOkay = Color(0xFF0891B2); // Cyan-600
  static const Color moodNotGreat = Color(0xFFD97706); // Amber-600
  static const Color moodPoor = Color(0xFFDC2626); // Red-600

  // Legacy mood colors for backward compatibility
  static const Color moodVeryHappy = moodExcellent;
  static const Color moodHappy = moodGood;
  static const Color moodNeutral = moodOkay;
  static const Color moodSad = moodNotGreat;
  static const Color moodVerySad = moodPoor;

  // Gray scale - for backward compatibility
  static const Color lightGray = surfaceContainer;
  static const Color mediumGray = outlineVariant;
  static const Color darkGray = textTertiary;

  // Gradients - More sophisticated
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF8B5CF6), Color(0xFF7C3AED)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF06B6D4), Color(0xFF0891B2)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFF43F5E), Color(0xFFE11D48)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFFFFFFFF), Color(0xFFFAFAFA)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient surfaceGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFFFFFFFF), Color(0xFFFAFAFA)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient shimmerGradient = LinearGradient(
    begin: Alignment(-1.0, -0.3),
    end: Alignment(1.0, 0.3),
    colors: [
      Color(0xFFE4E4E7),
      Color(0xFFF4F4F5),
      Color(0xFFE4E4E7),
    ],
    stops: [0.0, 0.5, 1.0],
  );

  // Shadow Colors
  static const Color shadowLight = Color(0x0A000000);
  static const Color shadowMedium = Color(0x14000000);
  static const Color shadowHeavy = Color(0x1F000000);

  // Helper methods
  static Color getMoodColor(int moodLevel) {
    if (moodLevel >= 9) return moodExcellent;
    if (moodLevel >= 7) return moodGood;
    if (moodLevel >= 5) return moodOkay;
    if (moodLevel >= 3) return moodNotGreat;
    return moodPoor;
  }

  // Helper method to get mood surface color
  static Color getMoodSurfaceColor(int moodLevel) {
    if (moodLevel >= 9) return successSurface;
    if (moodLevel >= 7) return successSurface;
    if (moodLevel >= 5) return infoSurface;
    if (moodLevel >= 3) return warningSurface;
    return errorSurface;
  }

  static Color getStressColor(int stressLevel) {
    if (stressLevel <= 3) return success;
    if (stressLevel <= 6) return warning;
    return error;
  }

  static Color getEnergyColor(int energyLevel) {
    if (energyLevel >= 7) return success;
    if (energyLevel >= 4) return warning;
    return error;
  }

  // Helper method to get status color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
}
