import 'package:flutter/material.dart';
import '../../utils/app_colors.dart';
import '../../widgets/custom_button.dart';

class TherapistDetailScreen extends StatefulWidget {
  final String therapistId;

  const TherapistDetailScreen({super.key, required this.therapistId});

  @override
  State<TherapistDetailScreen> createState() => _TherapistDetailScreenState();
}

class _TherapistDetailScreenState extends State<TherapistDetailScreen> {
  // Mock therapist data - in real app, this would be fetched from API
  late Map<String, dynamic> therapist;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTherapistDetails();
  }

  void _loadTherapistDetails() {
    // Simulate API call
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        therapist = {
          'id': widget.therapistId,
          'firstName': 'Dr. <PERSON>',
          'lastName': '<PERSON>',
          'credentials': ['PhD', 'LCSW'],
          'specializations': [
            'anxiety',
            'depression',
            'trauma',
            'relationships'
          ],
          'therapyApproaches': [
            'cognitive_behavioral_therapy',
            'mindfulness_based_therapy'
          ],
          'bio':
              'Dr. <PERSON> is a licensed clinical social worker with over 10 years of experience helping individuals overcome anxiety, depression, and trauma. She specializes in cognitive behavioral therapy and mindfulness-based approaches.',
          'personalStatement':
              'I believe that everyone has the capacity for growth and healing. My approach is collaborative, compassionate, and tailored to each individual\'s unique needs and goals.',
          'yearsOfExperience': 10,
          'education': [
            {
              'degree': 'PhD in Clinical Psychology',
              'institution': 'University of California, Los Angeles',
              'graduationYear': 2013
            },
            {
              'degree': 'MSW in Clinical Social Work',
              'institution': 'Columbia University',
              'graduationYear': 2010
            }
          ],
          'sessionTypes': ['video_call', 'in_person'],
          'pricing': {
            'sessionFee': 150,
            'currency': 'USD',
            'acceptsInsurance': true,
            'insuranceProviders': ['Blue Cross', 'Aetna', 'Cigna']
          },
          'ratings': {'average': 4.8, 'count': 127},
          'location': {'city': 'Los Angeles', 'state': 'CA'},
          'isAcceptingNewClients': true,
          'availability': {
            'monday': {
              'isAvailable': true,
              'timeSlots': [
                {'startTime': '09:00', 'endTime': '17:00'}
              ]
            },
            'tuesday': {
              'isAvailable': true,
              'timeSlots': [
                {'startTime': '09:00', 'endTime': '17:00'}
              ]
            },
            'wednesday': {
              'isAvailable': true,
              'timeSlots': [
                {'startTime': '09:00', 'endTime': '17:00'}
              ]
            },
            'thursday': {
              'isAvailable': true,
              'timeSlots': [
                {'startTime': '09:00', 'endTime': '17:00'}
              ]
            },
            'friday': {
              'isAvailable': true,
              'timeSlots': [
                {'startTime': '09:00', 'endTime': '15:00'}
              ]
            },
            'saturday': {'isAvailable': false, 'timeSlots': []},
            'sunday': {'isAvailable': false, 'timeSlots': []}
          },
          'reviews': [
            {
              'rating': 5,
              'comment':
                  'Dr. Johnson has been incredibly helpful in my journey with anxiety. Her approach is both professional and compassionate.',
              'isAnonymous': true,
              'createdAt': '2024-01-15'
            },
            {
              'rating': 5,
              'comment':
                  'Excellent therapist! She really listens and provides practical tools for managing stress.',
              'isAnonymous': false,
              'createdAt': '2024-01-10'
            },
            {
              'rating': 4,
              'comment':
                  'Very knowledgeable and patient. Would recommend to anyone dealing with relationship issues.',
              'isAnonymous': true,
              'createdAt': '2024-01-05'
            }
          ]
        };
        _isLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Therapist Details'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('${therapist['firstName']} ${therapist['lastName']}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // Share therapist profile
            },
          ),
          IconButton(
            icon: const Icon(Icons.favorite_border),
            onPressed: () {
              // Add to favorites
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header Section
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppColors.primaryGradient,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              child: Column(
                children: [
                  // Profile Picture and Basic Info
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 50,
                        backgroundColor: Colors.white,
                        child: Text(
                          therapist['firstName'][0] + therapist['lastName'][0],
                          style: const TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${therapist['firstName']} ${therapist['lastName']}',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              (therapist['credentials'] as List).join(', '),
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.white70,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(
                                  Icons.star,
                                  size: 20,
                                  color: Colors.amber,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${therapist['ratings']['average']} (${therapist['ratings']['count']} reviews)',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.white70,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Quick Stats
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _StatCard(
                        icon: Icons.work,
                        label: 'Experience',
                        value: '${therapist['yearsOfExperience']} years',
                      ),
                      _StatCard(
                        icon: Icons.attach_money,
                        label: 'Session Fee',
                        value: '\$${therapist['pricing']['sessionFee']}',
                      ),
                      _StatCard(
                        icon: Icons.location_on,
                        label: 'Location',
                        value:
                            '${therapist['location']['city']}, ${therapist['location']['state']}',
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Content Sections
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Availability Status
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: therapist['isAcceptingNewClients']
                          ? AppColors.success.withOpacity(0.1)
                          : AppColors.error.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: therapist['isAcceptingNewClients']
                            ? AppColors.success
                            : AppColors.error,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          therapist['isAcceptingNewClients']
                              ? Icons.check_circle
                              : Icons.cancel,
                          color: therapist['isAcceptingNewClients']
                              ? AppColors.success
                              : AppColors.error,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          therapist['isAcceptingNewClients']
                              ? 'Currently accepting new clients'
                              : 'Not accepting new clients',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: therapist['isAcceptingNewClients']
                                ? AppColors.success
                                : AppColors.error,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // About Section
                  _SectionCard(
                    title: 'About',
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          therapist['bio'],
                          style: const TextStyle(
                            fontSize: 16,
                            height: 1.5,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        if (therapist['personalStatement'] != null) ...[
                          const SizedBox(height: 16),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppColors.primary.withOpacity(0.05),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: AppColors.primary.withOpacity(0.2),
                              ),
                            ),
                            child: Text(
                              '"${therapist['personalStatement']}"',
                              style: const TextStyle(
                                fontSize: 16,
                                fontStyle: FontStyle.italic,
                                height: 1.5,
                                color: AppColors.textPrimary,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Specializations
                  _SectionCard(
                    title: 'Specializations',
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          (therapist['specializations'] as List).map((spec) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: AppColors.secondary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            spec.toString().replaceAll('_', ' ').toUpperCase(),
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: AppColors.secondary,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Therapy Approaches
                  _SectionCard(
                    title: 'Therapy Approaches',
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: (therapist['therapyApproaches'] as List)
                          .map((approach) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.check_circle,
                                size: 16,
                                color: AppColors.primary,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  approach
                                      .toString()
                                      .replaceAll('_', ' ')
                                      .toUpperCase(),
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Education
                  _SectionCard(
                    title: 'Education',
                    child: Column(
                      children: (therapist['education'] as List).map((edu) {
                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppColors.surfaceVariant,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.school,
                                color: AppColors.primary,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      edu['degree'],
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.textPrimary,
                                      ),
                                    ),
                                    Text(
                                      '${edu['institution']} • ${edu['graduationYear']}',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Session Details
                  _SectionCard(
                    title: 'Session Details',
                    child: Column(
                      children: [
                        _DetailRow(
                          icon: Icons.videocam,
                          label: 'Session Types',
                          value: (therapist['sessionTypes'] as List)
                              .join(', ')
                              .replaceAll('_', ' '),
                        ),
                        _DetailRow(
                          icon: Icons.attach_money,
                          label: 'Session Fee',
                          value:
                              '\$${therapist['pricing']['sessionFee']} per session',
                        ),
                        _DetailRow(
                          icon: Icons.health_and_safety,
                          label: 'Insurance',
                          value: therapist['pricing']['acceptsInsurance']
                              ? 'Accepts insurance'
                              : 'Does not accept insurance',
                        ),
                        if (therapist['pricing']['acceptsInsurance']) ...[
                          _DetailRow(
                            icon: Icons.list,
                            label: 'Insurance Providers',
                            value: (therapist['pricing']['insuranceProviders']
                                    as List)
                                .join(', '),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Reviews Section
                  _SectionCard(
                    title: 'Reviews (${therapist['ratings']['count']})',
                    child: Column(
                      children: [
                        // Rating Summary
                        Row(
                          children: [
                            Text(
                              therapist['ratings']['average'].toString(),
                              style: const TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: List.generate(5, (index) {
                                    return Icon(
                                      index <
                                              therapist['ratings']['average']
                                                  .floor()
                                          ? Icons.star
                                          : Icons.star_border,
                                      size: 20,
                                      color: Colors.amber,
                                    );
                                  }),
                                ),
                                Text(
                                  'Based on ${therapist['ratings']['count']} reviews',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Recent Reviews
                        ...(therapist['reviews'] as List).take(3).map((review) {
                          return Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.surfaceVariant,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Row(
                                      children: List.generate(5, (index) {
                                        return Icon(
                                          index < review['rating']
                                              ? Icons.star
                                              : Icons.star_border,
                                          size: 16,
                                          color: Colors.amber,
                                        );
                                      }),
                                    ),
                                    const Spacer(),
                                    Text(
                                      _formatDate(review['createdAt']),
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  review['comment'],
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),

                        if ((therapist['reviews'] as List).length > 3) ...[
                          TextButton(
                            onPressed: () {
                              // Show all reviews
                            },
                            child: const Text('View all reviews'),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Action Buttons
                  if (therapist['isAcceptingNewClients']) ...[
                    CustomButton(
                      text: 'Book Consultation',
                      onPressed: () {
                        _showBookingDialog(context);
                      },
                      width: double.infinity,
                      icon: Icons.calendar_today,
                    ),
                    const SizedBox(height: 12),
                  ],

                  CustomButton(
                    text: 'Send Message',
                    onPressed: () {
                      // Send message to therapist
                    },
                    isOutlined: true,
                    width: double.infinity,
                    icon: Icons.message,
                  ),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showBookingDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Book Consultation'),
        content: const Text('Booking functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to booking screen
            },
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  String _formatDate(String dateStr) {
    final date = DateTime.parse(dateStr);
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 30) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

class _StatCard extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _StatCard({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }
}

class _SectionCard extends StatelessWidget {
  final String title;
  final Widget child;

  const _SectionCard({
    required this.title,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            child,
          ],
        ),
      ),
    );
  }
}

class _DetailRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _DetailRow({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: AppColors.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
