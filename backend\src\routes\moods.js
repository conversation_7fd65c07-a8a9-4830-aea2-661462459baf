const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Mood = require('../models/Mood');
const { protectUser } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// All routes are protected and for users only
router.use(protectUser);

// Validation middleware
const validateMoodEntry = [
  body('moodLevel').isInt({ min: 1, max: 10 }).withMessage('Mood level must be between 1 and 10'),
  body('moodType').isIn([
    'very_sad', 'sad', 'neutral', 'happy', 'very_happy',
    'anxious', 'stressed', 'calm', 'excited', 'angry',
    'frustrated', 'content', 'overwhelmed', 'peaceful', 'energetic'
  ]).withMessage('Invalid mood type'),
  body('emotions').optional().isArray().withMessage('Emotions must be an array'),
  body('triggers').optional().isArray().withMessage('Triggers must be an array'),
  body('activities').optional().isArray().withMessage('Activities must be an array'),
  body('sleepHours').optional().isFloat({ min: 0, max: 24 }).withMessage('Sleep hours must be between 0 and 24'),
  body('energyLevel').optional().isInt({ min: 1, max: 10 }).withMessage('Energy level must be between 1 and 10'),
  body('stressLevel').optional().isInt({ min: 1, max: 10 }).withMessage('Stress level must be between 1 and 10'),
  body('notes').optional().isLength({ max: 500 }).withMessage('Notes cannot exceed 500 characters')
];

// @desc    Create mood entry
// @route   POST /api/moods
// @access  Private (User)
const createMoodEntry = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const moodData = {
    ...req.body,
    user: req.user._id
  };

  // Check if user already has a mood entry for today
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const existingMood = await Mood.findOne({
    user: req.user._id,
    date: {
      $gte: today,
      $lt: tomorrow
    }
  });

  if (existingMood) {
    return res.status(400).json({
      success: false,
      message: 'Mood entry already exists for today. Use PUT to update.'
    });
  }

  const mood = await Mood.create(moodData);

  res.status(201).json({
    success: true,
    message: 'Mood entry created successfully',
    data: mood
  });
});

// @desc    Get user's mood entries
// @route   GET /api/moods
// @access  Private (User)
const getMoodEntries = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    startDate,
    endDate,
    moodType,
    sortBy = 'date',
    sortOrder = 'desc'
  } = req.query;

  // Build query
  const query = { user: req.user._id };

  if (startDate || endDate) {
    query.date = {};
    if (startDate) query.date.$gte = new Date(startDate);
    if (endDate) query.date.$lte = new Date(endDate);
  }

  if (moodType) {
    query.moodType = moodType;
  }

  // Calculate pagination
  const skip = (page - 1) * limit;
  const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

  // Execute query
  const moods = await Mood.find(query)
    .sort(sort)
    .limit(limit * 1)
    .skip(skip);

  const total = await Mood.countDocuments(query);

  res.status(200).json({
    success: true,
    count: moods.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    },
    data: moods
  });
});

// @desc    Get mood entry by ID
// @route   GET /api/moods/:id
// @access  Private (User)
const getMoodEntry = asyncHandler(async (req, res) => {
  const mood = await Mood.findOne({
    _id: req.params.id,
    user: req.user._id
  });

  if (!mood) {
    return res.status(404).json({
      success: false,
      message: 'Mood entry not found'
    });
  }

  res.status(200).json({
    success: true,
    data: mood
  });
});

// @desc    Update mood entry
// @route   PUT /api/moods/:id
// @access  Private (User)
const updateMoodEntry = asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }

  const mood = await Mood.findOneAndUpdate(
    { _id: req.params.id, user: req.user._id },
    req.body,
    { new: true, runValidators: true }
  );

  if (!mood) {
    return res.status(404).json({
      success: false,
      message: 'Mood entry not found'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Mood entry updated successfully',
    data: mood
  });
});

// @desc    Delete mood entry
// @route   DELETE /api/moods/:id
// @access  Private (User)
const deleteMoodEntry = asyncHandler(async (req, res) => {
  const mood = await Mood.findOneAndDelete({
    _id: req.params.id,
    user: req.user._id
  });

  if (!mood) {
    return res.status(404).json({
      success: false,
      message: 'Mood entry not found'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Mood entry deleted successfully'
  });
});

// @desc    Get mood statistics
// @route   GET /api/moods/stats
// @access  Private (User)
const getMoodStats = asyncHandler(async (req, res) => {
  const { startDate, endDate, period = '30' } = req.query;

  let start, end;
  
  if (startDate && endDate) {
    start = new Date(startDate);
    end = new Date(endDate);
  } else {
    end = new Date();
    start = new Date();
    start.setDate(start.getDate() - parseInt(period));
  }

  const stats = await Mood.getMoodStats(req.user._id, start, end);
  const trends = await Mood.getMoodTrends(req.user._id, parseInt(period));

  res.status(200).json({
    success: true,
    data: {
      statistics: stats[0] || {
        averageMood: 0,
        averageEnergy: 0,
        averageStress: 0,
        totalEntries: 0
      },
      trends,
      period: {
        startDate: start,
        endDate: end,
        days: parseInt(period)
      }
    }
  });
});

// @desc    Get today's mood
// @route   GET /api/moods/today
// @access  Private (User)
const getTodaysMood = asyncHandler(async (req, res) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const mood = await Mood.findOne({
    user: req.user._id,
    date: {
      $gte: today,
      $lt: tomorrow
    }
  });

  res.status(200).json({
    success: true,
    data: mood,
    hasEntry: !!mood
  });
});

// Routes
router.post('/', validateMoodEntry, createMoodEntry);
router.get('/stats', getMoodStats);
router.get('/today', getTodaysMood);
router.get('/', getMoodEntries);
router.get('/:id', getMoodEntry);
router.put('/:id', validateMoodEntry, updateMoodEntry);
router.delete('/:id', deleteMoodEntry);

module.exports = router;
