const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { protectUser } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');

const router = express.Router();

// All routes are protected and for users only
router.use(protectUser);

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private (User)
const getUserProfile = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id).select('-password');

  res.status(200).json({
    success: true,
    data: user
  });
});

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private (User)
const updateUserProfile = asyncHandler(async (req, res) => {
  const allowedFields = [
    'firstName', 'lastName', 'phoneNumber', 'location',
    'preferences', 'mentalHealthProfile'
  ];

  const updateData = {};
  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      updateData[key] = req.body[key];
    }
  });

  const user = await User.findByIdAndUpdate(
    req.user._id,
    updateData,
    { new: true, runValidators: true }
  ).select('-password');

  res.status(200).json({
    success: true,
    message: 'Profile updated successfully',
    data: user
  });
});

// @desc    Update user preferences
// @route   PUT /api/users/preferences
// @access  Private (User)
const updateUserPreferences = asyncHandler(async (req, res) => {
  const user = await User.findByIdAndUpdate(
    req.user._id,
    { preferences: req.body },
    { new: true, runValidators: true }
  ).select('-password');

  res.status(200).json({
    success: true,
    message: 'Preferences updated successfully',
    data: user.preferences
  });
});

// @desc    Change password
// @route   PUT /api/users/change-password
// @access  Private (User)
const changePassword = asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      message: 'Current password and new password are required'
    });
  }

  if (newPassword.length < 6) {
    return res.status(400).json({
      success: false,
      message: 'New password must be at least 6 characters'
    });
  }

  const user = await User.findById(req.user._id).select('+password');

  const isCurrentPasswordCorrect = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordCorrect) {
    return res.status(400).json({
      success: false,
      message: 'Current password is incorrect'
    });
  }

  user.password = newPassword;
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Password changed successfully'
  });
});

// @desc    Delete user account
// @route   DELETE /api/users/account
// @access  Private (User)
const deleteUserAccount = asyncHandler(async (req, res) => {
  const { password } = req.body;

  if (!password) {
    return res.status(400).json({
      success: false,
      message: 'Password is required to delete account'
    });
  }

  const user = await User.findById(req.user._id).select('+password');

  const isPasswordCorrect = await user.comparePassword(password);
  if (!isPasswordCorrect) {
    return res.status(400).json({
      success: false,
      message: 'Password is incorrect'
    });
  }

  // Soft delete - deactivate account
  user.isActive = false;
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Account deactivated successfully'
  });
});

// @desc    Upload profile picture
// @route   POST /api/users/profile-picture
// @access  Private (User)
const uploadProfilePicture = asyncHandler(async (req, res) => {
  // This would typically use multer and cloudinary
  // For now, just accept a URL
  const { profilePictureUrl } = req.body;

  if (!profilePictureUrl) {
    return res.status(400).json({
      success: false,
      message: 'Profile picture URL is required'
    });
  }

  const user = await User.findByIdAndUpdate(
    req.user._id,
    { profilePicture: profilePictureUrl },
    { new: true }
  ).select('-password');

  res.status(200).json({
    success: true,
    message: 'Profile picture updated successfully',
    data: {
      profilePicture: user.profilePicture
    }
  });
});

// Routes
router.get('/profile', getUserProfile);
router.put('/profile', updateUserProfile);
router.put('/preferences', updateUserPreferences);
router.put('/change-password', changePassword);
router.delete('/account', deleteUserAccount);
router.post('/profile-picture', uploadProfilePicture);

module.exports = router;
